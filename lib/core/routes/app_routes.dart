import 'package:echipta/assets/app_constants.dart';
import 'package:echipta/features/auth/presentation/auth_screen.dart';
import 'package:echipta/features/auth/presentation/pincode_screen.dart';
import 'package:echipta/features/auth/presentation/registration_screen.dart';
import 'package:echipta/features/auth/presentation/set_pincode_screen.dart';
import 'package:echipta/features/auth/presentation/take_picture_screen.dart';
import 'package:echipta/features/auth/presentation/team_screen.dart';
import 'package:echipta/features/auth/presentation/verification_screen.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:echipta/features/home/<USER>/notification_screen.dart';
import 'package:echipta/features/order/presentation/order_idcard_screen.dart';
import 'package:echipta/features/order/presentation/panorama_screen.dart';
import 'package:echipta/features/order/presentation/seat_id_screen.dart';
import 'package:echipta/features/order/presentation/select_seat_screen.dart';
import 'package:echipta/features/order/presentation/sector_id_screen.dart';
import 'package:echipta/features/order/presentation/select_sector_screen.dart';
import 'package:echipta/features/intro/select_language_screen.dart';
import 'package:echipta/features/intro/splash_screen.dart';
import 'package:echipta/features/navigation/presentation/navigation_screen.dart';
import 'package:echipta/features/order/presentation/order_screen.dart';
import 'package:echipta/features/order/presentation/status_screen.dart';
import 'package:echipta/features/profile/presentation/contact_screen.dart';
import 'package:echipta/features/profile/presentation/idcard_screen.dart';
import 'package:echipta/features/profile/presentation/language_screen.dart';
import 'package:echipta/features/profile/presentation/my_cards_screen.dart';
import 'package:echipta/features/profile/presentation/order_history_screen.dart';
import 'package:echipta/features/gift/presentation/gift_screen.dart';
import 'package:echipta/features/profile/presentation/privacy_policy_screen.dart';
import 'package:echipta/features/gift/presentation/matches_for_gift_screen.dart';
import 'package:echipta/features/profile/presentation/profile_edit_screen.dart';
import 'package:echipta/features/profile/presentation/settings_screen.dart';
import 'package:echipta/features/profile/presentation/my_tickets_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_alice/alice.dart';
import 'package:go_router/go_router.dart';

import '../../features/cart/presentation/cart_screen.dart';
import '../../features/home/<USER>/notification_detail_screen.dart';

class AppRouter {
  static const String splash = "/";
  static const String language = "/language";
  static const String auth = "/auth";
  static const String verification = "/verification";
  static const String signin = "/signin";
  static const String setPincode = "/setPincode";
  static const String pincode = "/pincode";
  static const String navigator = "/navigator";
  static const String takePicture = "/takePicture";
  static const String team = "/team";
  static const String sector = "/sector";
  static const String seat = "/seat/:sector";
  static const String cards = "/cards";
  static const String present = "/present";
  static const String selectMatchScreen = "/selectMatchScreen";
  static const String idcard = "/idcard";
  static const String tickets = "/tickets";
  static const String orderHistory = "/orderHistory";
  static const String settings = "/settings";
  static const String contact = "/contact";
  static const String privacy = "/privacy";
  static const String panorama = "/panorama";
  static const String order = "/order";
  static const String cart = "/cart";
  static const String profileEdit = "/profileEdit";
  static const String idsector = "/idsector";
  static const String idseat = "/idseat";
  static const String orderid = "/orderid";
  static const String lang = "/lang";
  static const String orderStatus = "/orderStatus";
  static const String notifications = "/notifications";
  static const String notificationDetail = "/notificationDetail";
}

final GlobalKey<NavigatorState> _rootNavigatorKey = GlobalKey(debugLabel: "root");
final Alice alice = Alice(
  navigatorKey: _rootNavigatorKey,
  showNotification: true,
  showInspectorOnShake: true,
  darkTheme: false,
);

final appRouter = GoRouter(
  initialLocation: AppRouter.splash,
  navigatorKey: AppConstants.forProduction ? _rootNavigatorKey : alice.getNavigatorKey(),
  routes: [
    GoRoute(path: AppRouter.splash, builder: (context, state) => SplashScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.language, builder: (context, state) => SelectLanguageScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.auth, builder: (context, state) => AuthScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.verification, builder: (context, state) => VerificationScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.signin, builder: (context, state) => RegistrationScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.setPincode, builder: (context, state) => SetPincodeScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.pincode, builder: (context, state) => PincodeScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.navigator, builder: (context, state) => NavigationScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.takePicture, builder: (context, state) => TakePictureScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.team, builder: (context, state) => TeamScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.cards, builder: (context, state) => MyCardsScreen(key: state.pageKey)),
    GoRoute(
      path: AppRouter.sector,
      builder: (context, state) {
        return SelectSectorScreen(key: state.pageKey, match: state.extra as MatchEntity);
      },
    ),
    GoRoute(path: AppRouter.present, builder: (context, state) => GiftScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.selectMatchScreen, builder: (context, state) => MatchesForGiftScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.idcard, builder: (context, state) => IdCardScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.tickets, builder: (context, state) => TicketsScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.orderHistory, builder: (context, state) => OrderHistoryScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.settings, builder: (context, state) => SettingsScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.contact, builder: (context, state) => ContactScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.privacy, builder: (context, state) => PrivacyPolicyScreen(key: state.pageKey)),
    GoRoute(path: AppRouter.cart, builder: (context, state) => CartScreen(key: state.pageKey)),
    GoRoute(
      path: AppRouter.seat,
      name: "seat",
      builder: (context, state) {
        return SelectSeatScreen(key: state.pageKey, sector: state.pathParameters["sector"] ?? "");
      },
    ),
    GoRoute(
      path: AppRouter.panorama,
      builder: (context, state) => PonaramaScreen(key: state.pageKey, isId: state.extra as bool),
    ),
    GoRoute(path: AppRouter.order, builder: (context, state) => OrderScreen(key: state.pageKey)),
    GoRoute(
      path: AppRouter.profileEdit,
      builder: (context, state) {
        return ProfileEditScreen(key: state.pageKey);
      },
    ),
    GoRoute(
      path: AppRouter.idsector,
      builder: (context, state) {
        return SectorIdScreen(key: state.pageKey);
      },
    ),
    GoRoute(
      path: AppRouter.idseat,
      builder: (context, state) {
        return SeatIdScreen(sector: state.extra.toString(), key: state.pageKey);
      },
    ),
    GoRoute(
      path: AppRouter.orderid,
      builder: (context, state) {
        return OrderIdcardScreen(key: state.pageKey);
      },
    ),
    GoRoute(path: AppRouter.lang, builder: (context, state) => LanguageScreen(key: state.pageKey)),
    GoRoute(
      name: "orderStatus",
      path: AppRouter.orderStatus,
      builder: (context, state) {
        // Safely handle orderId with null checking
        int orderId = 0;
        if (state.extra != null) {
          if (state.extra is int) {
            orderId = state.extra as int;
          } else if (state.extra is String) {
            orderId = int.tryParse(state.extra as String) ?? 0;
          }
        }
        return StatusScreen(key: state.pageKey, orderId: orderId);
      },
    ),
    GoRoute(
      path: AppRouter.notifications,
      builder: (context, state) {
        return NotificationScreen(key: state.pageKey);
      },
    ),
    GoRoute(
      path: AppRouter.notificationDetail,
      builder: (context, state) {
        return NotificationDetailScreen(key: state.pageKey, newsId: state.extra as int);
      },
    ),
  ],
);
