import 'dart:async';
import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/themes/app_theme.dart';
import 'package:echipta/core/utils/size_config.dart';
import 'package:echipta/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:echipta/features/ball_rating/presentation/bloc/ball_rating_bloc.dart';
import 'package:echipta/features/chat/presentation/bloc/chat_bloc.dart';
import 'package:echipta/features/gift/presentation/bloc/gift_bloc.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/features/ticket/presentation/bloc/ticket_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:echipta/core/services/notification_service.dart';

import 'features/cart/presentation/bloc/cart_bloc.dart';

void main() {
  runZonedGuarded(
    () async {
      WidgetsFlutterBinding.ensureInitialized();

      await EasyLocalization.ensureInitialized();
      await StorageRepository.getInstance();
      await setupLocator();

      // Initialize notifications
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
      await NotificationService.initialize();
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
        ),
      );
      runApp(
        OverlaySupport(
          child: EasyLocalization(
            path: "assets/locales",
            saveLocale: true,
            supportedLocales: const [Locale("uz"), Locale("ru")],
            fallbackLocale: const Locale("uz"),
            startLocale: const Locale("uz"),
            child: const MyApp(),
          ),
        ),
      );
    },
    (error, stack) {
      log("ERROR HANDLED BY RUNZONE: $error ,\nSTACK: $stack");
    },
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // Set navigation context after first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        NotificationService.setNavigationContext(context);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    SizeConfig().init(context);
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => AuthBloc()..add(const CheckStatusEvent())),
        BlocProvider(create: (context) => ProfileBloc()..add(GetMeEvent())),
        BlocProvider(create: (context) => HomeBloc()),
        BlocProvider(create: (context) => BallRatingBloc()),
        BlocProvider(create: (context) => ChatBloc()..add(GetChatsEvent())),
        BlocProvider(create: (context) => TicketBloc()),
        BlocProvider(create: (context) => OrderBloc()),
        BlocProvider(create: (context) => CartBloc()),
        BlocProvider(create: (context) => GiftBloc()),
      ],
      child: MaterialApp.router(
        routerConfig: appRouter,
        title: 'Echipta',
        debugShowCheckedModeBanner: false,
        localizationsDelegates: context.localizationDelegates,
        supportedLocales: context.supportedLocales,
        locale: context.locale,
        theme: AppTheme.lightTheme,
      ),
    );
  }
}
