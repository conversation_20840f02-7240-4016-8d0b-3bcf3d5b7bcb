// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: constant_identifier_names

abstract class  LocaleKeys {
  static const confirm = 'confirm';
  static const login = 'login';
  static const sendCode = 'sendCode';
  static const enterCode = 'enterCode';
  static const resendCodeTime = 'resendCodeTime';
  static const resendCode = 'resendCode';
  static const createAccount = 'createAccount';
  static const fio = 'fio';
  static const birthDate = 'birthDate';
  static const selectRegion = 'selectRegion';
  static const allRight = 'allRight';
  static const regInfo = 'regInfo';
  static const singin = 'singin';
  static const enterAddress = 'enterAddress';
  static const selfieForIDCard = 'selfieForIDCard';
  static const selectBrightnessPlace = 'selectBrightnessPlace';
  static const nearToCamera = 'nearToCamera';
  static const takePicture = 'takePicture';
  static const confirmed = 'confirmed';
  static const welcome = 'welcome';
  static const accountVerified = 'accountVerified';
  static const enter = 'enter';
  static const skip = 'skip';
  static const setPinCode = 'setPinCode';
  static const set = 'set';
  static const selectTeam = 'selectTeam';
  static const logout = 'logout';
  static const helloEnterPin = 'helloEnterPin';
  static const needHelp = 'needHelp';
  static const resultsSearch = 'resultsSearch';
  static const search = 'search';
  static const main = 'main';
  static const rating = 'rating';
  static const help = 'help';
  static const account = 'account';
  static const currentMatch = 'currentMatch';
  static const products = 'products';
  static const support = 'support';
  static const enterMessage = 'enterMessage';
  static const first = 'first';
  static const second = 'second';
  static const third = 'third';
  static const vip = 'vip';
  static const fan = 'fan';
  static const guest = 'guest';
  static const emptyPlaces = 'emptyPlaces';
  static const notEmptyPlaces = 'notEmptyPlaces';
  static const selected = 'selected';
  static const validation = 'validation';
  static const phoneNumber = 'phoneNumber';
  static const currentTicket = 'currentTicket';
  static const started = 'started';
  static const finished = 'finished';
  static const onSale = 'onSale';
  static const somethingWentWrong = 'somethingWentWrong';
  static const fanRanking = 'fanRanking';
  static const settings = 'settings';
  static const gift = 'gift';
  static const orderhistory = 'orderhistory';
  static const myid = 'myid';
  static const mytickets = 'mytickets';
  static const privacyPolicy = 'privacyPolicy';
  static const orderProduct = 'orderProduct';
  static const price = 'price';
  static const noCurrentTicket = 'noCurrentTicket';
  static const deliveryAddress = 'deliveryAddress';
  static const commonPrice = 'commonPrice';
  static const pay = 'pay';
  static const appPin = 'appPin';
  static const appLang = 'appLang';
  static const selectGame = 'selectGame';
  static const recieverId = 'recieverId';
  static const enterId = 'enterId';
  static const selectGame2 = 'selectGame2';
  static const selectMatch = 'selectMatch';
  static const selectSector = 'selectSector';
  static const sector = 'sector';
  static const type = 'type';
  static const emptySeats = 'emptySeats';
  static const select = 'select';
  static const price2 = 'price2';
  static const selectSeat = 'selectSeat';
  static const emptySeats2 = 'emptySeats2';
  static const row = 'row';
  static const seat = 'seat';
  static const idcardNumber = 'idcardNumber';
  static const idcardNumberCopied = 'idcardNumberCopied';
  static const recieptIsChecking = 'recieptIsChecking';
  static const error = 'error';
  static const paymentInfo = 'paymentInfo';
  static const game = 'game';
  static const date = 'date';
  static const ticketPrice = 'ticketPrice';
  static const time = 'time';
  static const paymentType = 'paymentType';
  static const profileBalance = 'profileBalance';
  static const alifPay = 'alifPay';
  static const endPay = 'endPay';
  static const reciever = 'reciever';
  static const viewPlace = 'viewPlace';
  static const viziualView = 'viziualView';
  static const pov = 'pov';
  static const notification = 'notification';
  static const mycards = 'mycards';
  static const profileBalance2 = 'profileBalance2';
  static const addCard = 'addCard';
  static const paymentHistory = 'paymentHistory';
  static const topup = 'topup';
  static const ballRating = 'ballRating';
  static const ballHistory = 'ballHistory';
  static const ballEarning = 'ballEarning';
  static const ballPromotions = 'ballPromotions';
  static const ballPurchase = 'ballPurchase';
  static const ballUsage = 'ballUsage';
  static const myBalls = 'myBalls';
  static const myPosition = 'myPosition';
  static const myGames = 'myGames';
  static const selectTicket = 'selectTicket';
  static const ballPurchaseDesc = 'ballPurchaseDesc';
  static const ballEarningDesc = 'ballEarningDesc';
  static const expiryDate = 'expiryDate';
  static const inactive = 'inactive';
  static const ball = 'ball';
  static const som = 'som';

}
