// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/ball_rating/domain/entities/rank_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'rank_model.g.dart';

/// Model for ball rating client data from the new API response structure
@JsonSerializable(fieldRename: FieldRename.snake)
class BallRatingClientModel extends BallRatingClientEntity {
  const BallRatingClientModel({
    super.rating,
    super.game,
    super.client_id,
    super.full_name,
    super.rank,
    super.is_current_user,
  });

  factory BallRatingClientModel.fromJson(Map<String, dynamic> json) =>
      _$BallRatingClientModelFromJson(json);

  Map<String, dynamic> toJson() => _$BallRatingClientModelToJson(this);

  BallRatingClientEntity toEntity() {
    return BallRatingClientEntity(
      rating: rating,
      game: game,
      client_id: client_id,
      full_name: full_name,
      rank: rank,
      is_current_user: is_current_user,
    );
  }
}

/// Model for current user ball rating data from the new API response structure
@JsonSerializable(fieldRename: FieldRename.snake)
class BallRatingCurrentUserModel extends BallRatingCurrentUserEntity {
  const BallRatingCurrentUserModel({
    super.client_id,
    super.full_name,
    super.rating,
    super.game,
    super.rank,
  });

  factory BallRatingCurrentUserModel.fromJson(Map<String, dynamic> json) =>
      _$BallRatingCurrentUserModelFromJson(json);

  Map<String, dynamic> toJson() => _$BallRatingCurrentUserModelToJson(this);

  BallRatingCurrentUserEntity toEntity() {
    return BallRatingCurrentUserEntity(
      client_id: client_id,
      full_name: full_name,
      rating: rating,
      game: game,
      rank: rank,
    );
  }
}

/// Model for the complete ball rating API response
@JsonSerializable(fieldRename: FieldRename.snake)
class BallRatingResponseModel {
  final List<BallRatingClientModel> top_clients;
  final BallRatingCurrentUserModel current_user;

  const BallRatingResponseModel({
    this.top_clients = const [],
    this.current_user = const BallRatingCurrentUserModel(),
  });

  factory BallRatingResponseModel.fromJson(Map<String, dynamic> json) =>
      _$BallRatingResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$BallRatingResponseModelToJson(this);

  BallRatingResponseEntity toEntity() {
    return BallRatingResponseEntity(
      top_clients: top_clients.map((client) => client.toEntity()).toList(),
      current_user: current_user.toEntity(),
    );
  }
}

/// Legacy model for backward compatibility - will be deprecated
/// Model for general user ranking data from /clients/rating endpoint
@JsonSerializable(fieldRename: FieldRename.snake)
class RankModel extends RankEntity {
  const RankModel({
    super.client_id,
    super.full_name,
    super.order_count,
    super.score,
  });

  factory RankModel.fromJson(Map<String, dynamic> json) =>
      _$RankModelFromJson(json);
}
