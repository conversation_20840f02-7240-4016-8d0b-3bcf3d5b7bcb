// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/ball_rating/domain/entities/rank_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'rank_model.g.dart';

/// Model for general user ranking data from /clients/rating endpoint
@JsonSerializable(fieldRename: FieldRename.snake)
class RankModel extends RankEntity {
  const RankModel({
    super.client_id,
    super.full_name,
    super.order_count,
    super.score,
  });

  factory RankModel.fromJson(Map<String, dynamic> json) =>
      _$RankModelFromJson(json);
}
