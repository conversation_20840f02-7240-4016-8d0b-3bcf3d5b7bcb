// ignore_for_file: public_member_api_docs, sort_constructors_first
// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/ball_rating/domain/entities/ball_user_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ball_user_model.g.dart';

@JsonSerializable()
class BallUserModel extends BallUserEntity {
  const BallUserModel({
    super.id,
    super.full_name,
    super.avatar,
    super.total_balls,
    super.total_games,
    super.total_months,
    super.position,
    super.ball_change,
  });

  factory BallUserModel.fromJson(Map<String, dynamic> json) =>
      _$BallUserModelFromJson(json);

  Map<String, dynamic> toJson() => _$BallUserModelToJson(this);

  factory BallUserModel.fromEntity(BallUserEntity entity) {
    return BallUserModel(
      id: entity.id,
      full_name: entity.full_name,
      avatar: entity.avatar,
      total_balls: entity.total_balls,
      total_games: entity.total_games,
      total_months: entity.total_months,
      position: entity.position,
      ball_change: entity.ball_change,
    );
  }

  BallUserEntity toEntity() {
    return BallUserEntity(
      id: id,
      full_name: full_name,
      avatar: avatar,
      total_balls: total_balls,
      total_games: total_games,
      total_months: total_months,
      position: position,
      ball_change: ball_change,
    );
  }
}
