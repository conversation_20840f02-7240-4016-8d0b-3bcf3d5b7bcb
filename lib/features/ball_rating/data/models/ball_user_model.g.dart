// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ball_user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BallUserModel _$BallUserModelFromJson(Map<String, dynamic> json) =>
    BallUserModel(
      id: (json['id'] as num?)?.toInt() ?? 0,
      full_name: json['full_name'] as String? ?? "",
      avatar: json['avatar'] as String? ?? "",
      total_balls: (json['total_balls'] as num?)?.toInt() ?? 0,
      total_games: (json['total_games'] as num?)?.toInt() ?? 0,
      total_months: (json['total_months'] as num?)?.toInt() ?? 0,
      position: (json['position'] as num?)?.toInt() ?? 0,
      ball_change: (json['ball_change'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$BallUserModelToJson(BallUserModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'full_name': instance.full_name,
      'avatar': instance.avatar,
      'total_balls': instance.total_balls,
      'total_games': instance.total_games,
      'total_months': instance.total_months,
      'position': instance.position,
      'ball_change': instance.ball_change,
    };
