import 'package:dio/dio.dart';
import 'package:echipta/core/api/dio_settings.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/features/ball_rating/data/models/ball_user_model.dart';

abstract class BallRatingDataSource {
  Future<List<Map<String, dynamic>>> getBallHistory();
  Future<List<Map<String, dynamic>>> getBallEarningMatches();
  Future<List<Map<String, dynamic>>> getBallPromotions();
  Future<List<Map<String, dynamic>>> getBallPackages();
  Future<String?> purchaseBallPackage(int packageId);
  Future<BallUserModel> getCurrentUserBallInfo();
}

class BallRatingDataSourceImpl implements BallRatingDataSource {
  final Dio _dio = serviceLocator<DioSettings>().dio;

  @override
  Future<List<Map<String, dynamic>>> getBallHistory() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get(
        "/ball/history",
        options: Options(headers: {"Authorization": token}),
      );
      
      final List<dynamic> data = response.data['data'] ?? [];
      return data.cast<Map<String, dynamic>>();
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '142');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getBallEarningMatches() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get(
        "/ball/earning-matches",
        options: Options(headers: {"Authorization": token}),
      );
      
      final List<dynamic> data = response.data['data'] ?? [];
      return data.cast<Map<String, dynamic>>();
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '143');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getBallPromotions() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get(
        "/ball/promotions",
        options: Options(headers: {"Authorization": token}),
      );
      
      final List<dynamic> data = response.data['data'] ?? [];
      return data.cast<Map<String, dynamic>>();
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '144');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getBallPackages() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get(
        "/ball/packages",
        options: Options(headers: {"Authorization": token}),
      );
      
      final List<dynamic> data = response.data['data'] ?? [];
      return data.cast<Map<String, dynamic>>();
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '145');
    }
  }

  @override
  Future<String?> purchaseBallPackage(int packageId) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.post(
        "/ball/purchase",
        data: {"package_id": packageId},
        options: Options(headers: {"Authorization": token}),
      );
      
      return response.data['payment_url'];
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '146');
    }
  }

  @override
  Future<BallUserModel> getCurrentUserBallInfo() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get(
        "/ball/user-info",
        options: Options(headers: {"Authorization": token}),
      );
      
      return BallUserModel.fromJson(response.data['data']);
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '147');
    }
  }
}
