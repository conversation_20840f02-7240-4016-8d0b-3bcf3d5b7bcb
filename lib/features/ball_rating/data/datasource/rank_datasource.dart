import 'package:dio/dio.dart';
import 'package:echipta/core/api/dio_settings.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/features/ball_rating/data/models/rank_model.dart';

/// Datasource for general user ranking data from /clients/rating endpoint
abstract class RankDatasource {
  Future<List<RankModel>> getRanks();
}

class RankDatasourceImpl implements RankDatasource {
  final Dio _dio = serviceLocator<DioSettings>().dio;
  
  @override
  Future<List<RankModel>> getRanks() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get(
        "/clients/rating",
        queryParameters: {
          'limit': 100, // Request more items instead of default 20
          'page': 1,
        },
        options: Options(headers: {"Authorization": token}),
      );
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        final data = response.data["data"];

        // Handle new API response format with top_clients
        if (data is Map<String, dynamic> && data.containsKey('top_clients')) {
          final topClients = data['top_clients'] as List;
          return topClients
              .map((e) => RankModel(
                    client_id: e['client_id'] ?? 0,
                    full_name: e['full_name'] ?? '',
                    order_count: e['game'] ?? 0, // Map 'game' to 'order_count'
                    score: e['rating'] ?? 0,     // Map 'rating' to 'score'
                  ))
              .toList();
        }

        // Handle legacy API response format (direct array)
        else if (data is List) {
          return data
              .map((e) => RankModel.fromJson(e as Map<String, dynamic>))
              .toList();
        }

        // Fallback: empty list if format is unexpected
        else {
          return <RankModel>[];
        }
      } else {
        final message = (response.data as Map<String, dynamic>)
            .values
            .toString()
            .replaceAll(
              RegExp(r'[\[\]]'),
              '',
            );
        throw CustomException(
          message: message,
          code: '${response.statusCode}',
        );
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }
}
