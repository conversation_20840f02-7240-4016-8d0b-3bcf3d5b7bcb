import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/ball_rating/data/datasource/ball_rating_datasource.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_user_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_history_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_earning_match_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_promotion_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_package_entity.dart';
import 'package:echipta/features/ball_rating/domain/repository/ball_rating_repository.dart';

class BallRatingRepositoryImpl implements BallRatingRepository {
  final BallRatingDataSource _dataSource = serviceLocator<BallRatingDataSourceImpl>();

  @override
  Future<Either<Failure, List<BallHistoryEntity>>> getBallHistory() async {
    try {
      final result = await _dataSource.getBallHistory();
      final entities = result.map((json) => BallHistoryEntity(
        id: json['id'] ?? 0,
        title: json['title'] ?? '',
        description: json['description'] ?? '',
        ball_amount: json['ball_amount'] ?? 0,
        created_at: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
        transaction_type: json['transaction_type'] ?? 'earned',
      )).toList();
      return Right(entities);
    } catch (e) {
      return Left(ServerFailure(errorMessage: e.toString(), statusCode: 500, errorKey: 'ball_history_error'));
    }
  }

  @override
  Future<Either<Failure, List<BallEarningMatchEntity>>> getBallEarningMatches() async {
    try {
      final result = await _dataSource.getBallEarningMatches();
      final entities = result.map((json) => BallEarningMatchEntity(
        id: json['id'] ?? 0,
        title: json['title'] ?? '',
        match_date: DateTime.tryParse(json['match_date'] ?? '') ?? DateTime.now(),
        team1_name: json['team1_name'] ?? '',
        team1_logo: json['team1_logo'] ?? '',
        team2_name: json['team2_name'] ?? '',
        team2_logo: json['team2_logo'] ?? '',
        card_color: json['card_color'] ?? 'brown',
        is_available: json['is_available'] ?? true,
      )).toList();
      return Right(entities);
    } catch (e) {
      return Left(ServerFailure(errorMessage: e.toString(), statusCode: 500, errorKey: 'ball_earning_error'));
    }
  }

  @override
  Future<Either<Failure, List<BallPromotionEntity>>> getBallPromotions() async {
    try {
      final result = await _dataSource.getBallPromotions();
      final entities = result.map((json) => BallPromotionEntity(
        id: json['id'] ?? 0,
        title: json['title'] ?? '',
        description: json['description'] ?? '',
        ball_reward: json['ball_reward'] ?? 0,
        is_active: json['is_active'] ?? true,
        expires_at: json['expires_at'] != null ? DateTime.tryParse(json['expires_at']) : null,
      )).toList();
      return Right(entities);
    } catch (e) {
      return Left(ServerFailure(errorMessage: e.toString(), statusCode: 500, errorKey: 'ball_promotions_error'));
    }
  }

  @override
  Future<Either<Failure, List<BallPackageEntity>>> getBallPackages() async {
    try {
      final result = await _dataSource.getBallPackages();
      final entities = result.map((json) => BallPackageEntity(
        id: json['id'] ?? 0,
        ball_amount: json['ball_amount'] ?? 0,
        price_uzs: json['price_uzs'] ?? 0,
        is_selected: false,
        is_available: json['is_available'] ?? true,
      )).toList();
      return Right(entities);
    } catch (e) {
      return Left(ServerFailure(errorMessage: e.toString(), statusCode: 500, errorKey: 'ball_packages_error'));
    }
  }

  @override
  Future<Either<Failure, String?>> purchaseBallPackage(int packageId) async {
    try {
      final result = await _dataSource.purchaseBallPackage(packageId);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(errorMessage: e.toString(), statusCode: 500, errorKey: 'ball_purchase_error'));
    }
  }

  @override
  Future<Either<Failure, BallUserEntity>> getCurrentUserBallInfo() async {
    try {
      final result = await _dataSource.getCurrentUserBallInfo();
      return Right(result.toEntity());
    } catch (e) {
      return Left(ServerFailure(errorMessage: e.toString(), statusCode: 500, errorKey: 'ball_user_info_error'));
    }
  }
}
