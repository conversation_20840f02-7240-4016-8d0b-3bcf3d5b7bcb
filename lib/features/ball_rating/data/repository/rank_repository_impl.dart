import 'package:dio/dio.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/exceptions/exceptions.dart';
import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/ball_rating/data/datasource/rank_datasource.dart';
import 'package:echipta/features/ball_rating/domain/entities/rank_entity.dart';
import 'package:echipta/features/ball_rating/domain/repository/rank_repository.dart';

/// Repository implementation for general user ranking data
class RankRepositoryImpl implements RankRepository {
  final RankDatasource _datasource = serviceLocator<RankDatasourceImpl>();
  
  @override
  Future<Either<Failure, List<RankEntity>>> getRanks() async {
    try {
      final result = await _datasource.getRanks();
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey));
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }
}
