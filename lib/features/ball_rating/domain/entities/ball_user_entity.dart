// ignore_for_file: public_member_api_docs, sort_constructors_first
// ignore_for_file: non_constant_identifier_names

import 'package:equatable/equatable.dart';

class BallUserEntity extends Equatable {
  final int id;
  final String full_name;
  final String avatar;
  final int total_balls;
  final int total_games;
  final int total_months;
  final int position;
  final int ball_change; // +/- indicator

  const BallUserEntity({
    this.id = 0,
    this.full_name = "",
    this.avatar = "",
    this.total_balls = 0,
    this.total_games = 0,
    this.total_months = 0,
    this.position = 0,
    this.ball_change = 0,
  });

  @override
  List<Object?> get props => [
        id,
        full_name,
        avatar,
        total_balls,
        total_games,
        total_months,
        position,
        ball_change,
      ];

  BallUserEntity copyWith({
    int? id,
    String? full_name,
    String? avatar,
    int? total_balls,
    int? total_games,
    int? total_months,
    int? position,
    int? ball_change,
  }) {
    return BallUserEntity(
      id: id ?? this.id,
      full_name: full_name ?? this.full_name,
      avatar: avatar ?? this.avatar,
      total_balls: total_balls ?? this.total_balls,
      total_games: total_games ?? this.total_games,
      total_months: total_months ?? this.total_months,
      position: position ?? this.position,
      ball_change: ball_change ?? this.ball_change,
    );
  }
}
