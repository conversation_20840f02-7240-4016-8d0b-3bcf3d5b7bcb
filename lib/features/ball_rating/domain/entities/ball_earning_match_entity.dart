// ignore_for_file: public_member_api_docs, sort_constructors_first
// ignore_for_file: non_constant_identifier_names

import 'package:equatable/equatable.dart';

class BallEarningMatchEntity extends Equatable {
  final int id;
  final String title;
  final DateTime match_date;
  final String team1_name;
  final String team1_logo;
  final String team2_name;
  final String team2_logo;
  final String card_color; // 'brown', 'pink', 'blue', 'yellow'
  final bool is_available;

  BallEarningMatchEntity({
    this.id = 0,
    this.title = "",
    DateTime? match_date,
    this.team1_name = "",
    this.team1_logo = "",
    this.team2_name = "",
    this.team2_logo = "",
    this.card_color = "brown",
    this.is_available = true,
  }) : match_date = match_date ?? DateTime(2024, 1, 1);

  @override
  List<Object?> get props => [
        id,
        title,
        match_date,
        team1_name,
        team1_logo,
        team2_name,
        team2_logo,
        card_color,
        is_available,
      ];

  BallEarningMatchEntity copyWith({
    int? id,
    String? title,
    DateTime? match_date,
    String? team1_name,
    String? team1_logo,
    String? team2_name,
    String? team2_logo,
    String? card_color,
    bool? is_available,
  }) {
    return BallEarningMatchEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      match_date: match_date ?? this.match_date,
      team1_name: team1_name ?? this.team1_name,
      team1_logo: team1_logo ?? this.team1_logo,
      team2_name: team2_name ?? this.team2_name,
      team2_logo: team2_logo ?? this.team2_logo,
      card_color: card_color ?? this.card_color,
      is_available: is_available ?? this.is_available,
    );
  }
}
