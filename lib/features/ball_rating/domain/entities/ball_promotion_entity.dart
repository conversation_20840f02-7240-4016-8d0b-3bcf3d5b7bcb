// ignore_for_file: public_member_api_docs, sort_constructors_first
// ignore_for_file: non_constant_identifier_names

import 'package:equatable/equatable.dart';

class BallPromotionEntity extends Equatable {
  final int id;
  final String title;
  final String description;
  final int ball_reward;
  final bool is_active;
  final DateTime? expires_at;

  const BallPromotionEntity({
    this.id = 0,
    this.title = "",
    this.description = "",
    this.ball_reward = 0,
    this.is_active = true,
    this.expires_at,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        ball_reward,
        is_active,
        expires_at,
      ];

  BallPromotionEntity copyWith({
    int? id,
    String? title,
    String? description,
    int? ball_reward,
    bool? is_active,
    DateTime? expires_at,
  }) {
    return BallPromotionEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      ball_reward: ball_reward ?? this.ball_reward,
      is_active: is_active ?? this.is_active,
      expires_at: expires_at ?? this.expires_at,
    );
  }
}
