// ignore_for_file: public_member_api_docs, sort_constructors_first
// ignore_for_file: non_constant_identifier_names

import 'package:equatable/equatable.dart';

class BallPackageEntity extends Equatable {
  final int id;
  final int ball_amount;
  final int price_uzs;
  final bool is_selected;
  final bool is_available;

  const BallPackageEntity({
    this.id = 0,
    this.ball_amount = 0,
    this.price_uzs = 0,
    this.is_selected = false,
    this.is_available = true,
  });

  @override
  List<Object?> get props => [
        id,
        ball_amount,
        price_uzs,
        is_selected,
        is_available,
      ];

  BallPackageEntity copyWith({
    int? id,
    int? ball_amount,
    int? price_uzs,
    bool? is_selected,
    bool? is_available,
  }) {
    return BallPackageEntity(
      id: id ?? this.id,
      ball_amount: ball_amount ?? this.ball_amount,
      price_uzs: price_uzs ?? this.price_uzs,
      is_selected: is_selected ?? this.is_selected,
      is_available: is_available ?? this.is_available,
    );
  }
}
