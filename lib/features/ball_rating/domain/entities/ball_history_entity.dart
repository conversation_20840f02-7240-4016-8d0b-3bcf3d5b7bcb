// ignore_for_file: public_member_api_docs, sort_constructors_first
// ignore_for_file: non_constant_identifier_names

import 'package:equatable/equatable.dart';

class BallHistoryEntity extends Equatable {
  final int id;
  final String title;
  final String description;
  final int ball_amount;
  final DateTime created_at;
  final String transaction_type; // 'earned', 'spent', 'purchased'

  BallHistoryEntity({
    this.id = 0,
    this.title = "",
    this.description = "",
    this.ball_amount = 0,
    DateTime? created_at,
    this.transaction_type = "earned",
  }) : created_at = created_at ?? DateTime(2024, 1, 1);

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        ball_amount,
        created_at,
        transaction_type,
      ];

  BallHistoryEntity copyWith({
    int? id,
    String? title,
    String? description,
    int? ball_amount,
    DateTime? created_at,
    String? transaction_type,
  }) {
    return BallHistoryEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      ball_amount: ball_amount ?? this.ball_amount,
      created_at: created_at ?? this.created_at,
      transaction_type: transaction_type ?? this.transaction_type,
    );
  }
}
