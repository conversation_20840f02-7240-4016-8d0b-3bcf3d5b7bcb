// ignore_for_file: public_member_api_docs, sort_constructors_first
// ignore_for_file: non_constant_identifier_names

import 'package:equatable/equatable.dart';

/// Entity for ball rating client data from the new API response structure
/// Used for displaying individual clients in the ranking table
class BallRatingClientEntity extends Equatable {
  final int rating;
  final int game;
  final int client_id;
  final String full_name;
  final int rank;
  final bool is_current_user;

  const BallRatingClientEntity({
    this.rating = 0,
    this.game = 0,
    this.client_id = 0,
    this.full_name = "",
    this.rank = 0,
    this.is_current_user = false,
  });

  @override
  List<Object?> get props => [
        rating,
        game,
        client_id,
        full_name,
        rank,
        is_current_user,
      ];

  BallRatingClientEntity copyWith({
    int? rating,
    int? game,
    int? client_id,
    String? full_name,
    int? rank,
    bool? is_current_user,
  }) {
    return BallRatingClientEntity(
      rating: rating ?? this.rating,
      game: game ?? this.game,
      client_id: client_id ?? this.client_id,
      full_name: full_name ?? this.full_name,
      rank: rank ?? this.rank,
      is_current_user: is_current_user ?? this.is_current_user,
    );
  }
}

/// Entity for current user ball rating data from the new API response structure
class BallRatingCurrentUserEntity extends Equatable {
  final int client_id;
  final String full_name;
  final int rating;
  final int game;
  final int rank;

  const BallRatingCurrentUserEntity({
    this.client_id = 0,
    this.full_name = "",
    this.rating = 0,
    this.game = 0,
    this.rank = 0,
  });

  @override
  List<Object?> get props => [
        client_id,
        full_name,
        rating,
        game,
        rank,
      ];

  BallRatingCurrentUserEntity copyWith({
    int? client_id,
    String? full_name,
    int? rating,
    int? game,
    int? rank,
  }) {
    return BallRatingCurrentUserEntity(
      client_id: client_id ?? this.client_id,
      full_name: full_name ?? this.full_name,
      rating: rating ?? this.rating,
      game: game ?? this.game,
      rank: rank ?? this.rank,
    );
  }
}

/// Entity for the complete ball rating API response
class BallRatingResponseEntity extends Equatable {
  final List<BallRatingClientEntity> top_clients;
  final BallRatingCurrentUserEntity current_user;

  const BallRatingResponseEntity({
    this.top_clients = const [],
    this.current_user = const BallRatingCurrentUserEntity(),
  });

  @override
  List<Object?> get props => [top_clients, current_user];

  BallRatingResponseEntity copyWith({
    List<BallRatingClientEntity>? top_clients,
    BallRatingCurrentUserEntity? current_user,
  }) {
    return BallRatingResponseEntity(
      top_clients: top_clients ?? this.top_clients,
      current_user: current_user ?? this.current_user,
    );
  }
}

/// Legacy entity for backward compatibility - will be deprecated
/// Entity for general user ranking data from /clients/rating endpoint
/// Used for the main DataTable2 display in ball rating view
class RankEntity extends Equatable {
  final int order_count;
  final int client_id;
  final String full_name;
  final int score;

  const RankEntity({
    this.order_count = 0,
    this.client_id = 0,
    this.full_name = "",
    this.score = 0,
  });

  @override
  List<Object?> get props => [
        order_count,
        client_id,
        full_name,
        score,
      ];

  RankEntity copyWith({
    int? order_count,
    int? client_id,
    String? full_name,
    int? score,
  }) {
    return RankEntity(
      order_count: order_count ?? this.order_count,
      client_id: client_id ?? this.client_id,
      full_name: full_name ?? this.full_name,
      score: score ?? this.score,
    );
  }
}
