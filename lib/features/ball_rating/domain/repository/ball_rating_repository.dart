import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_user_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_history_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_earning_match_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_promotion_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_package_entity.dart';

abstract class BallRatingRepository {
  Future<Either<Failure, List<BallHistoryEntity>>> getBallHistory();
  Future<Either<Failure, List<BallEarningMatchEntity>>> getBallEarningMatches();
  Future<Either<Failure, List<BallPromotionEntity>>> getBallPromotions();
  Future<Either<Failure, List<BallPackageEntity>>> getBallPackages();
  Future<Either<Failure, String?>> purchaseBallPackage(int packageId);
  Future<Either<Failure, BallUserEntity>> getCurrentUserBallInfo();
}
