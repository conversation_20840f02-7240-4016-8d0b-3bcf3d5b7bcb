import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:gap/gap.dart';
import 'package:image/image.dart' as img;
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/services.dart';
import 'dart:math';

class BallUsageBottomSheet extends StatefulWidget {
  const BallUsageBottomSheet({super.key});

  @override
  State<BallUsageBottomSheet> createState() => _BallUsageBottomSheetState();
}

class _BallUsageBottomSheetState extends State<BallUsageBottomSheet>
    with TickerProviderStateMixin {
  int? selectedMatchIndex;
  final Map<int, Color> _matchColors = {}; // Primary colors (team2)
  final Map<int, Color> _team1Colors = {}; // Team1 colors for gradient
  final Map<int, AnimationController> _colorAnimationControllers = {};
  final Map<int, Animation<Color?>> _colorAnimations = {};

  final List<MatchData> matches = [
    MatchData(
      id: 1,
      team1: 'Andijon',
      team2: 'Barcelona',
      team1Logo:
          'https://upload.wikimedia.org/wikipedia/uz/d/d8/PFK_Andijon.png',
      team2Logo:
          'https://upload.wikimedia.org/wikipedia/en/thumb/4/47/FC_Barcelona_%28crest%29.svg/2020px-FC_Barcelona_%28crest%29.svg.png',
      date: '28.06.2024',
      time: '17:00',
      stadium: 'Andijon Arena',
      ballCost: 50,
    ),
    MatchData(
      id: 2,
      team1: 'Andijon',
      team2: 'Surxon',
      team1Logo:
          'https://upload.wikimedia.org/wikipedia/uz/d/d8/PFK_Andijon.png',
      team2Logo:
          'https://api.pfcsurkhon.uz/uploads/images/club/2022-12-07T18-03-46-788Z_SURXON.png',
      date: '02.07.2024',
      time: '19:00',
      stadium: 'Andijon Arena',
      ballCost: 45,
    ),
    MatchData(
      id: 3,
      team1: 'Andijon',
      team2: 'Neftchi',
      team1Logo:
          'https://upload.wikimedia.org/wikipedia/uz/d/d8/PFK_Andijon.png',
      team2Logo:
          'https://upload.wikimedia.org/wikipedia/uz/thumb/7/71/Neftchi-2022-08.png/500px-Neftchi-2022-08.png',
      date: '08.07.2024',
      time: '18:30',
      stadium: 'Andijon Arena',
      ballCost: 60,
    ),
    MatchData(
      id: 4,
      team1: 'Andijon',
      team2: 'Barcelona',
      team1Logo:
          'https://upload.wikimedia.org/wikipedia/uz/d/d8/PFK_Andijon.png',
      team2Logo:
          'https://upload.wikimedia.org/wikipedia/en/thumb/4/47/FC_Barcelona_%28crest%29.svg/2020px-FC_Barcelona_%28crest%29.svg.png',
      date: '15.07.2024',
      time: '20:00',
      stadium: 'Andijon Arena',
      ballCost: 55,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _extractColorsFromMatches();
  }

  @override
  void dispose() {
    for (final controller in _colorAnimationControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _initializeAnimations() {
    for (final match in matches) {
      final controller = AnimationController(
        duration: const Duration(milliseconds: 1500),
        // 1.5 second smooth transition
        vsync: this,
      );
      _colorAnimationControllers[match.id] = controller;
    }
  }

  Future<void> _extractColorsFromMatches() async {
    print('🚀 Starting color extraction for ${matches.length} matches');

    // Set initial vibrant colors immediately for instant UI
    final initialColors = [
      ///Primary color
      const Color(0xFF000000), // Black
      const Color(0xFF000000), // Black
      const Color(0xFF000000), // Black
      const Color(0xFF000000), // Black
    ];

    // Set initial colors first for immediate UI feedback
    for (final match in matches) {
      _matchColors[match.id] =
          initialColors[(match.id - 1) % initialColors.length];
    }

    if (mounted) {
      setState(() {});
    }

    // Extract actual colors from team logos in parallel for better performance
    final colorExtractionFutures = matches.map((match) async {
      try {
        print('🎨 Extracting color for ${match.team1} vs ${match.team2}');
        final extractedColor = await _extractDominantColor(match.team2Logo);
        if (mounted) {
          _animateColorTransition(match.id, extractedColor);
          print(
            '✅ Color updated for match ${match.id}: ${extractedColor.toString()}',
          );
        }
      } catch (e) {
        print('❌ Color extraction failed for ${match.team2}: $e');
        // Keep the initial color if extraction fails
      }
    });

    // Wait for all extractions to complete
    await Future.wait(colorExtractionFutures);
    print('🎉 Color extraction completed for all matches');
  }

  void _animateColorTransition(int matchId, Color newColor) {
    final currentColor = _matchColors[matchId] ?? const Color(0xFF342783);
    final controller = _colorAnimationControllers[matchId];

    if (controller != null) {
      // Create color tween animation
      final colorTween = ColorTween(begin: currentColor, end: newColor);

      final animation = colorTween.animate(
        CurvedAnimation(
          parent: controller,
          curve: Curves.easeInOut, // Smooth ease in/out curve
        ),
      );

      _colorAnimations[matchId] = animation;

      // Listen to animation updates
      animation.addListener(() {
        if (mounted) {
          setState(() {
            _matchColors[matchId] = animation.value ?? newColor;
          });
        }
      });

      // Start the animation
      controller.forward();
    } else {
      // Fallback if no controller
      setState(() {
        _matchColors[matchId] = newColor;
      });
    }
  }

  Future<Color> _extractDominantColor(String imageUrl) async {
    try {
      print('🎨 Starting color extraction for: $imageUrl');

      // Download the image
      final response = await NetworkAssetBundle(
        Uri.parse(imageUrl),
      ).load(imageUrl);
      final bytes = response.buffer.asUint8List();

      // Decode the image
      final image = img.decodeImage(bytes);
      if (image == null) {
        print('❌ Failed to decode image');
        throw Exception('Failed to decode image');
      }

      print('✅ Image decoded: ${image.width}x${image.height}');

      // Resize for faster processing
      final resized = img.copyResize(image, width: 50, height: 50);

      // Extract colors
      final colorCounts = <int, int>{};

      for (int y = 0; y < resized.height; y++) {
        for (int x = 0; x < resized.width; x++) {
          final pixel = resized.getPixel(x, y);
          final r = pixel.r.toInt();
          final g = pixel.g.toInt();
          final b = pixel.b.toInt();

          // Skip very light or very dark colors
          final brightness = (r + g + b) / 3;
          if (brightness < 50 || brightness > 200) continue;

          // Create color key (reduce precision for grouping)
          final colorKey = ((r ~/ 32) << 16) | ((g ~/ 32) << 8) | (b ~/ 32);
          colorCounts[colorKey] = (colorCounts[colorKey] ?? 0) + 1;
        }
      }

      if (colorCounts.isEmpty) {
        print('❌ No suitable colors found');
        throw Exception('No suitable colors found');
      }

      // Find the most common color
      final dominantColorKey =
          colorCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;

      // Convert back to full color
      final r = ((dominantColorKey >> 16) & 0x7) * 32 + 16;
      final g = ((dominantColorKey >> 8) & 0x7) * 32 + 16;
      final b = (dominantColorKey & 0x7) * 32 + 16;

      final extractedColor = Color.fromRGBO(r, g, b, 1.0);

      // Enhance saturation for better visual impact
      final hsl = HSLColor.fromColor(extractedColor);
      final enhancedColor =
          hsl
              .withSaturation((hsl.saturation * 1.3).clamp(0.0, 1.0))
              .withLightness(hsl.lightness.clamp(0.3, 0.7))
              .toColor();

      print('🎨 Extracted color: ${enhancedColor.toString()}');
      return enhancedColor;
    } catch (e) {
      print('❌ Color extraction failed: $e');
      // Return a random vibrant color instead of always the same fallback
      final random = Random();
      final vibrantColors = [
        const Color(0xFF1E88E5), // Blue
        const Color(0xFF43A047), // Green
        const Color(0XFFE53935), // Red
        const Color(0XFFFB8C00), // Orange
        const Color(0xFF8E24AA), // Purple
        const Color(0xFF00ACC1), // Cyan
        const Color(0xFFD81B60), // Pink
        const Color(0xFF6D4C41), // Brown
      ];
      return vibrantColors[random.nextInt(vibrantColors.length)];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      ///Padding for bottom
      padding: EdgeInsets.only(bottom: context.padding.bottom),
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: const BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.mediumGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Text(
                  LocaleKeys.ballUsage.tr(),
                  style: context.textTheme.headlineMedium!.copyWith(
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1B1B1B),
                    fontSize: 18,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppColors.fillColor,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(
                      Icons.close,
                      size: 20,
                      color: AppColors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Description
                  Text(
                    'Ballingizni chiptaga almashtirish uchun chipta olmoqchi bo\'lgan o\'yiningizni tanlang',
                    style: const TextStyle(
                      color: Color(0xFF97999B),
                      fontSize: 14,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w500,
                      height: 1.4,
                    ),
                  ),
                  const Gap(24),

                  // Match selection
                  Text(
                    'O\'yinni tanlang:',
                    style: context.textTheme.titleMedium!.copyWith(
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF1B1B1B),
                    ),
                  ),
                  const Gap(16),
                  ...matches.asMap().entries.map((entry) {
                    final index = entry.key;
                    final match = entry.value;
                    return _buildEnhancedMatchCard(match, index);
                  }).toList(),

                  const Gap(32),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedMatchCard(MatchData match, int index) {
    final matchColor = _matchColors[match.id] ?? const Color(0xFF342783);
    final lightColor = matchColor.withValues(alpha: 0.4);
    final accentColor = matchColor.withValues(alpha: 0.2);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // Navigate directly to stadium seat selection
            _navigateToSeatSelection(match);
          },
          borderRadius: BorderRadius.circular(16),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 1500),
            // Same duration as color animation
            curve: Curves.easeInOut,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [lightColor, Colors.white],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: matchColor.withValues(alpha: 0.2),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: matchColor.withValues(alpha: 0.1),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // Header with match info
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: accentColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${match.ballCost} ball',
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Text(
                      '${match.date} | ${match.time}',
                      style: TextStyle(
                        color: Colors.black.withValues(alpha: 0.7),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),

                const Gap(20),

                // Teams section
                Row(
                  children: [
                    // Team 1
                    Expanded(
                      child: Column(
                        children: [
                          SizedBox(
                            width: 50,
                            height: 50,
                            child: CachedNetworkImage(
                              imageUrl: match.team1Logo,
                              fit: BoxFit.contain,
                              progressIndicatorBuilder:
                                  (context, url, progress) => SizedBox(
                                    width: 20,
                                    height: 20,
                                    child:
                                        const CircularProgressIndicator.adaptive(),
                                  ),
                              errorWidget:
                                  (context, url, error) => Container(
                                    decoration: BoxDecoration(
                                      color: Colors.grey[100],
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Icon(
                                      Icons.sports_soccer,
                                      size: 28,
                                      color: Colors.grey,
                                    ),
                                  ),
                            ),
                          ),
                          const Gap(12),
                          AnimatedDefaultTextStyle(
                            duration: const Duration(milliseconds: 1500),
                            curve: Curves.easeInOut,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                            ),
                            textAlign: TextAlign.center,
                            child: Text(match.team1),
                          ),
                        ],
                      ),
                    ),

                    // VS Section
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        children: [
                          AnimatedContainer(
                            duration: const Duration(milliseconds: 1500),
                            curve: Curves.easeInOut,
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: Colors.black.withValues(alpha: 0.3),
                                width: 2,
                              ),
                              color: matchColor.withValues(alpha: 0.3),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: matchColor.withValues(alpha: 0.3),
                                  blurRadius: 12,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Center(
                              child: Text(
                                'VS',
                                style: TextStyle(
                                  color: Colors.black38,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w800,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Team 2
                    Expanded(
                      child: Column(
                        children: [
                          Container(
                            width: 50,
                            height: 50,
                            child: CachedNetworkImage(
                              imageUrl: match.team2Logo,
                              fit: BoxFit.contain,
                              progressIndicatorBuilder:
                                  (context, url, progress) => SizedBox(
                                width: 20,
                                height: 20,
                                child:
                                const CircularProgressIndicator.adaptive(),
                              ),
                              errorWidget:
                                  (context, url, error) => Container(
                                    decoration: BoxDecoration(
                                      color: Colors.grey[100],
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Icon(
                                      Icons.sports_soccer,
                                      size: 28,
                                      color: Colors.grey,
                                    ),
                                  ),
                            ),
                          ),
                          const Gap(12),
                          AnimatedDefaultTextStyle(
                            duration: const Duration(milliseconds: 1500),
                            curve: Curves.easeInOut,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                            ),
                            textAlign: TextAlign.center,
                            child: Text(match.team2),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const Gap(16),

                // Stadium info
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 16,
                        color: Colors.black.withValues(alpha: 0.7),
                      ),
                      const Gap(4),
                      Text(
                        match.stadium,
                        style: TextStyle(
                          color: Colors.black.withValues(alpha: 0.7),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToSeatSelection(MatchData match) {
    // Close the bottom sheet
    Navigator.pop(context);

    // Show success message for now (replace with actual navigation)
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '${match.team1} vs ${match.team2} o\'yini uchun o\'rindiq tanlash sahifasiga o\'tilmoqda...',
        ),
        backgroundColor: const Color(0xFF06BC49),
        duration: const Duration(seconds: 2),
      ),
    );

    // TODO: Navigate to stadium seat selection screen
    // Navigator.push(context, MaterialPageRoute(
    //   builder: (context) => SeatSelectionScreen(match: match),
    // ));
  }
}

// Data classes
class MatchData {
  final int id;
  final String team1;
  final String team2;
  final String team1Logo;
  final String team2Logo;
  final String date;
  final String time;
  final String stadium;
  final int ballCost;

  MatchData({
    required this.id,
    required this.team1,
    required this.team2,
    required this.team1Logo,
    required this.team2Logo,
    required this.date,
    required this.time,
    required this.stadium,
    required this.ballCost,
  });
}
