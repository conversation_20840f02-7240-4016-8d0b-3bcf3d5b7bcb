import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:intl/intl.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/ball_rating/presentation/bloc/ball_rating_bloc.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_promotion_entity.dart';
import 'package:echipta/features/common/widgets/w_empty.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:gap/gap.dart';

class BallPromotionsView extends StatelessWidget {
  const BallPromotionsView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BallRatingBloc, BallRatingState>(
      builder: (context, state) {
        if (state.promotionsStatus.isInProgress) {
          return const Center(child: CircularProgressIndicator.adaptive());
        } else if (state.promotionsStatus.isFailure) {
          return const Center(child: Text("Xatolik yuz berdi!"));
        } else if (state.promotionsStatus.isSuccess && state.promotions.isEmpty) {
          return WEmptyScreen();
        }

        return ListView.builder(
          padding: const EdgeInsets.all(20),
          itemCount: state.promotions.length,
          itemBuilder: (context, index) {
            final promotion = state.promotions[index];
            return _buildPromotionCard(context, promotion);
          },
        );
      },
    );
  }

  Widget _buildPromotionCard(BuildContext context, BallPromotionEntity promotion) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.green3,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.green.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with ball reward
          Row(
            children: [
              Expanded(
                child: Text(
                  promotion.title,
                  style: context.textTheme.bodyMedium!.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.black,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.green,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '+${promotion.ball_reward} ${LocaleKeys.ball.tr()}',
                  style: context.textTheme.bodySmall!.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const Gap(12),
          
          // Description
          Text(
            promotion.description,
            style: context.textTheme.bodySmall!.copyWith(
              color: AppColors.black,
              height: 1.4,
            ),
          ),
          
          // Expiry date if available
          if (promotion.expires_at != null) ...[
            const Gap(12),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: AppColors.darkGrey,
                ),
                const Gap(4),
                Text(
                  '${LocaleKeys.expiryDate.tr()}: ${DateFormat('dd.MM.yyyy').format(promotion.expires_at!)}',
                  style: context.textTheme.bodySmall!.copyWith(
                    color: AppColors.darkGrey,
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ],
          
          // Status indicator
          if (!promotion.is_active) ...[
            const Gap(12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                LocaleKeys.inactive.tr(),
                style: context.textTheme.bodySmall!.copyWith(
                  color: AppColors.red,
                  fontWeight: FontWeight.w500,
                  fontSize: 11,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
