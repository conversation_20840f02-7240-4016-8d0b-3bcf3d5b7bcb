import 'package:echipta/assets/app_assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:formz/formz.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/ball_rating/presentation/bloc/ball_rating_bloc.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_package_entity.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:gap/gap.dart';

class BallPurchaseView extends StatelessWidget {
  const BallPurchaseView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.75,
      decoration: const BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.mediumGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Text(
                  'Ball sotib olish',
                  style: context.textTheme.headlineMedium!.copyWith(
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1B1B1B),
                    fontSize: 18,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppColors.fillColor,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(
                      Icons.close,
                      size: 20,
                      color: AppColors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Package list
          Expanded(
            child: BlocBuilder<BallRatingBloc, BallRatingState>(
              builder: (context, state) {
                if (state.packagesStatus.isInProgress) {
                  return const Center(child: CircularProgressIndicator.adaptive());
                } else if (state.packagesStatus.isFailure) {
                  return const Center(child: Text("Xatolik yuz berdi!"));
                } else if (state.packagesStatus.isSuccess && state.packages.isEmpty) {
                  return const Center(child: Text("Paketlar mavjud emas"));
                }

                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  itemCount: state.packages.length,
                  itemBuilder: (context, index) {
                    final package = state.packages[index];
                    return _buildPackageRow(context, package, state.selectedPackageId);
                  },
                );
              },
            ),
          ),

          // Purchase button
          Container(
            width: double.infinity,
            height: 56,
            margin: EdgeInsets.fromLTRB(20, 16, 20, context.padding.bottom + 20),
            child: BlocBuilder<BallRatingBloc, BallRatingState>(
              builder: (context, state) {
                final hasSelection = state.selectedPackageId != null;
                return ElevatedButton(
                  onPressed: hasSelection ? () {
                    context.read<BallRatingBloc>().add(
                      PurchaseBallPackageEvent(
                        packageId: state.selectedPackageId!,
                        onSuccess: () {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Ball to\'plami muvaffaqiyatli sotib olindi!'),
                              backgroundColor: const Color(0xFF06BC49),
                              duration: const Duration(seconds: 3),
                            ),
                          );
                        },
                        onError: (error) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Xatolik: $error'),
                              backgroundColor: const Color(0xFFEB1F28),
                              duration: const Duration(seconds: 3),
                            ),
                          );
                        },
                      ),
                    );
                  } : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: hasSelection ? const Color(0xFF342783) : const Color(0xFFE5E7EB),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                    elevation: 0,
                  ),
                  child: state.purchaseStatus.isInProgress
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          'Ball sotib olish',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: hasSelection ? Colors.white : const Color(0xFF9CA3AF),
                          ),
                        ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPackageRow(BuildContext context, BallPackageEntity package, int? selectedPackageId) {
    final isSelected = selectedPackageId == package.id;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            context.read<BallRatingBloc>().add(
              SelectBallPackageEvent(packageId: package.id),
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFFF0F0F0),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF000000).withValues(alpha: 0.04),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Purple brand logo
                Container(
                  width: 48,
                  height: 48,
                  padding: const EdgeInsets.all(12),
                  decoration: const BoxDecoration(
                    color: AppColors.primary,
                    shape: BoxShape.circle,
                  ),
                  child: SvgPicture.asset(AppAssets.whiteLogo),
                ),
                const Gap(16),

                // Package content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${package.ball_amount} ball',
                        style: context.textTheme.titleMedium!.copyWith(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: const Color(0xFF1B1B1B),
                        ),
                      ),
                      const Gap(4),
                      Text(
                        '${_formatPrice(package.price_uzs)}uzs',
                        style: context.textTheme.bodyMedium!.copyWith(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF97999B),
                        ),
                      ),
                    ],
                  ),
                ),

                // Selection radio button
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isSelected ? const Color(0xFF06BC49) : const Color(0xFFE5E7EB),
                    border: Border.all(
                      color: isSelected ? const Color(0xFF06BC49) : const Color(0xFFD1D5DB),
                      width: 2,
                    ),
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        )
                      : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatPrice(int price) {
    return price.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]} ',
    );
  }
}
