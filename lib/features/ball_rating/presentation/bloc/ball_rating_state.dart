part of 'ball_rating_bloc.dart';

class BallRatingState extends Equatable {
  final FormzSubmissionStatus rankingStatus;        // Ball-specific ranking (/ball/ranking)
  final FormzSubmissionStatus generalRankingStatus; // General user ranking (/clients/rating) - for DataTable2
  final FormzSubmissionStatus historyStatus;
  final FormzSubmissionStatus earningMatchesStatus;
  final FormzSubmissionStatus promotionsStatus;
  final FormzSubmissionStatus packagesStatus;
  final FormzSubmissionStatus userInfoStatus;
  final FormzSubmissionStatus purchaseStatus;
  
  final List<BallUserEntity> ranking;           // Ball-specific ranking data
  final List<RankEntity> generalRanking;        // General user ranking data (for DataTable2)
  final List<BallHistoryEntity> history;
  final List<BallEarningMatchEntity> earningMatches;
  final List<BallPromotionEntity> promotions;
  final List<BallPackageEntity> packages;
  final BallUserEntity currentUser;
  
  final int currentTabIndex;
  final int? selectedPackageId;

  const BallRatingState({
    this.rankingStatus = FormzSubmissionStatus.initial,
    this.generalRankingStatus = FormzSubmissionStatus.initial,
    this.historyStatus = FormzSubmissionStatus.initial,
    this.earningMatchesStatus = FormzSubmissionStatus.initial,
    this.promotionsStatus = FormzSubmissionStatus.initial,
    this.packagesStatus = FormzSubmissionStatus.initial,
    this.userInfoStatus = FormzSubmissionStatus.initial,
    this.purchaseStatus = FormzSubmissionStatus.initial,
    this.ranking = const [],
    this.generalRanking = const [],
    this.history = const [],
    this.earningMatches = const [],
    this.promotions = const [],
    this.packages = const [],
    this.currentUser = const BallUserEntity(),
    this.currentTabIndex = 0,
    this.selectedPackageId,
  });

  BallRatingState copyWith({
    FormzSubmissionStatus? rankingStatus,
    FormzSubmissionStatus? generalRankingStatus,
    FormzSubmissionStatus? historyStatus,
    FormzSubmissionStatus? earningMatchesStatus,
    FormzSubmissionStatus? promotionsStatus,
    FormzSubmissionStatus? packagesStatus,
    FormzSubmissionStatus? userInfoStatus,
    FormzSubmissionStatus? purchaseStatus,
    List<BallUserEntity>? ranking,
    List<RankEntity>? generalRanking,
    List<BallHistoryEntity>? history,
    List<BallEarningMatchEntity>? earningMatches,
    List<BallPromotionEntity>? promotions,
    List<BallPackageEntity>? packages,
    BallUserEntity? currentUser,
    int? currentTabIndex,
    int? selectedPackageId,
  }) {
    return BallRatingState(
      rankingStatus: rankingStatus ?? this.rankingStatus,
      generalRankingStatus: generalRankingStatus ?? this.generalRankingStatus,
      historyStatus: historyStatus ?? this.historyStatus,
      earningMatchesStatus: earningMatchesStatus ?? this.earningMatchesStatus,
      promotionsStatus: promotionsStatus ?? this.promotionsStatus,
      packagesStatus: packagesStatus ?? this.packagesStatus,
      userInfoStatus: userInfoStatus ?? this.userInfoStatus,
      purchaseStatus: purchaseStatus ?? this.purchaseStatus,
      ranking: ranking ?? this.ranking,
      generalRanking: generalRanking ?? this.generalRanking,
      history: history ?? this.history,
      earningMatches: earningMatches ?? this.earningMatches,
      promotions: promotions ?? this.promotions,
      packages: packages ?? this.packages,
      currentUser: currentUser ?? this.currentUser,
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
      selectedPackageId: selectedPackageId ?? this.selectedPackageId,
    );
  }

  @override
  List<Object?> get props => [
        rankingStatus,
        generalRankingStatus,
        historyStatus,
        earningMatchesStatus,
        promotionsStatus,
        packagesStatus,
        userInfoStatus,
        purchaseStatus,
        ranking,
        generalRanking,
        history,
        earningMatches,
        promotions,
        packages,
        currentUser,
        currentTabIndex,
        selectedPackageId,
      ];
}
