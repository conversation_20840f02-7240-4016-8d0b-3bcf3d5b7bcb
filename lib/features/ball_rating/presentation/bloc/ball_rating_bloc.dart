import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_user_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_history_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_earning_match_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_promotion_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_package_entity.dart';
import 'package:echipta/features/ball_rating/domain/usecases/ball_rating_use_cases.dart';
import 'package:echipta/features/ball_rating/domain/entities/rank_entity.dart';
import 'package:echipta/features/ball_rating/domain/usecases/rank_use_cases.dart';

part 'ball_rating_event.dart';
part 'ball_rating_state.dart';

/// **CONSOLIDATED BALL RATING BLOC**
///
/// This bloc now handles ALL ball rating functionality, consolidating what was previously
/// split between RankBloc and BallRatingBloc. This eliminates confusion and provides
/// a single source of truth for all ranking-related data.
///
/// **DATA SOURCES:**
///
/// 1. **General Ranking** (`generalRanking` - List<RankEntity>):
///    - API: `/clients/rating`
///    - Status: REAL API DATA
///    - Usage: Main DataTable2 display in ball_rating_view
///    - Event: GetGeneralRankingEvent
///
///
/// 2. **Current User Ball Info** (`currentUser` - BallUserEntity):
///    - API: `/ball/user-info`
///    - Status: REAL API DATA (fixed from mock implementation)
///    - Usage: User's personal ball information
///    - Event: GetCurrentUserBallInfoEvent
///
/// 3. **Other Ball Features** (history, earning matches, promotions, packages):
///    - Status: MOCK DATA (for future implementation)
///    - Usage: Various ball-related features
///
/// **MIGRATION NOTES:**
/// - Moved RankBloc functionality into this bloc
/// - Fixed mock data in GetCurrentUserBallInfoEvent to use real API
/// - Added GetGeneralRankingEvent for DataTable2 data
/// - The old rating feature folder can be safely removed
class BallRatingBloc extends Bloc<BallRatingEvent, BallRatingState> {
  final GetBallHistoryUseCase _getBallHistoryUseCase = GetBallHistoryUseCase();
  final GetBallEarningMatchesUseCase _getBallEarningMatchesUseCase = GetBallEarningMatchesUseCase();
  final GetBallPromotionsUseCase _getBallPromotionsUseCase = GetBallPromotionsUseCase();
  final GetBallPackagesUseCase _getBallPackagesUseCase = GetBallPackagesUseCase();
  final PurchaseBallPackageUseCase _purchaseBallPackageUseCase = PurchaseBallPackageUseCase();
  final GetCurrentUserBallInfoUseCase _getCurrentUserBallInfoUseCase = GetCurrentUserBallInfoUseCase();
  final RankUseCase _getRankUseCase = RankUseCase(); // For general ranking (/clients/rating)

  BallRatingBloc() : super(const BallRatingState()) {
    on<GetGeneralRankingEvent>(_onGetGeneralRanking);
    on<GetBallHistoryEvent>(_onGetBallHistory);
    on<GetBallEarningMatchesEvent>(_onGetBallEarningMatches);
    on<GetBallPromotionsEvent>(_onGetBallPromotions);
    on<GetBallPackagesEvent>(_onGetBallPackages);
    on<GetCurrentUserBallInfoEvent>(_onGetCurrentUserBallInfo);
    on<SelectBallPackageEvent>(_onSelectBallPackage);
    on<PurchaseBallPackageEvent>(_onPurchaseBallPackage);
    on<ChangeTabEvent>(_onChangeTab);
  }

  /// Handles GetGeneralRankingEvent - fetches general user ranking from /clients/rating
  /// This is REAL API data used for the main DataTable2 display
  Future<void> _onGetGeneralRanking(
    GetGeneralRankingEvent event,
    Emitter<BallRatingState> emit,
  ) async {
    emit(state.copyWith(generalRankingStatus: FormzSubmissionStatus.inProgress));

    final result = await _getRankUseCase(NoParams());

    if (result.isRight) {
      emit(state.copyWith(
        generalRankingStatus: FormzSubmissionStatus.success,
        generalRanking: result.right,
      ));
    } else {
      emit(state.copyWith(generalRankingStatus: FormzSubmissionStatus.failure));
    }
  }

  Future<void> _onGetBallHistory(
    GetBallHistoryEvent event,
    Emitter<BallRatingState> emit,
  ) async {
    emit(state.copyWith(historyStatus: FormzSubmissionStatus.inProgress));

    // Mock data for ball history
    await Future.delayed(const Duration(milliseconds: 800));

    final mockHistory = [
      BallHistoryEntity(
        id: 1,
        title: "Tug‘ilgan kuningiz bilan!",
        description: "Tavallud ayyomingiz munosabati bilan sizga bonus ballar taqdim etildi",
        ball_amount: 25,
        created_at: DateTime.now().subtract(const Duration(days: 1)),
        transaction_type: "earned",
      ),
      BallHistoryEntity(
        id: 2,
        title: "Kundalik bonus",
        description: "Har kungi faollik uchun bonus ball",
        ball_amount: 10,
        created_at: DateTime.now().subtract(const Duration(days: 2)),
        transaction_type: "earned",
      ),
      BallHistoryEntity(
        id: 3,
        title: "Shtraf",
        description: "Stadionni iflos qilish va janjal chiqarish",
        ball_amount: -50,
        created_at: DateTime.now().subtract(const Duration(days: 3)),
        transaction_type: "spent",
      ),
      BallHistoryEntity(
        id: 4,
        title: "Ball to'plami",
        description: "100 ball to'plamini sotib oldingiz",
        ball_amount: 100,
        created_at: DateTime.now().subtract(const Duration(days: 5)),
        transaction_type: "purchased",
      ),
      BallHistoryEntity(
        id: 5,
        title: "Skaner nazoratidan o‘tish",
        description: "Ball oldingiz",
        ball_amount: 15,
        created_at: DateTime.now().subtract(const Duration(days: 7)),
        transaction_type: "earned",
      ),
    ];

    emit(state.copyWith(
      historyStatus: FormzSubmissionStatus.success,
      history: mockHistory,
    ));
  }

  Future<void> _onGetBallEarningMatches(
    GetBallEarningMatchesEvent event,
    Emitter<BallRatingState> emit,
  ) async {
    emit(state.copyWith(earningMatchesStatus: FormzSubmissionStatus.inProgress));

    // Mock data for ball earning matches
    await Future.delayed(const Duration(milliseconds: 600));

    final mockMatches = [
      BallEarningMatchEntity(
        id: 1,
        title: "Premier Liga",
        match_date: DateTime.now().add(const Duration(days: 2, hours: 3)),
        team1_name: "Arsenal",
        team1_logo: "https://via.placeholder.com/40",
        team2_name: "Chelsea",
        team2_logo: "https://via.placeholder.com/40",
        card_color: "brown",
        is_available: true,
      ),
      BallEarningMatchEntity(
        id: 2,
        title: "La Liga",
        match_date: DateTime.now().add(const Duration(days: 1, hours: 5)),
        team1_name: "Real Madrid",
        team1_logo: "https://via.placeholder.com/40",
        team2_name: "Barcelona",
        team2_logo: "https://via.placeholder.com/40",
        card_color: "pink",
        is_available: true,
      ),
      BallEarningMatchEntity(
        id: 3,
        title: "Serie A",
        match_date: DateTime.now().add(const Duration(days: 3, hours: 2)),
        team1_name: "Juventus",
        team1_logo: "https://via.placeholder.com/40",
        team2_name: "AC Milan",
        team2_logo: "https://via.placeholder.com/40",
        card_color: "blue",
        is_available: true,
      ),
      BallEarningMatchEntity(
        id: 4,
        title: "Bundesliga",
        match_date: DateTime.now().add(const Duration(days: 4, hours: 1)),
        team1_name: "Bayern",
        team1_logo: "https://via.placeholder.com/40",
        team2_name: "Dortmund",
        team2_logo: "https://via.placeholder.com/40",
        card_color: "yellow",
        is_available: true,
      ),
      BallEarningMatchEntity(
        id: 5,
        title: "Ligue 1",
        match_date: DateTime.now().add(const Duration(days: 5, hours: 4)),
        team1_name: "PSG",
        team1_logo: "https://via.placeholder.com/40",
        team2_name: "Marseille",
        team2_logo: "https://via.placeholder.com/40",
        card_color: "brown",
        is_available: true,
      ),
      BallEarningMatchEntity(
        id: 6,
        title: "Premier Liga",
        match_date: DateTime.now().add(const Duration(days: 6, hours: 2)),
        team1_name: "Man City",
        team1_logo: "https://via.placeholder.com/40",
        team2_name: "Liverpool",
        team2_logo: "https://via.placeholder.com/40",
        card_color: "pink",
        is_available: true,
      ),
    ];

    emit(state.copyWith(
      earningMatchesStatus: FormzSubmissionStatus.success,
      earningMatches: mockMatches,
    ));
  }

  Future<void> _onGetBallPromotions(
    GetBallPromotionsEvent event,
    Emitter<BallRatingState> emit,
  ) async {
    emit(state.copyWith(promotionsStatus: FormzSubmissionStatus.inProgress));

    // Mock data for ball promotions
    await Future.delayed(const Duration(milliseconds: 700));

    final mockPromotions = [
      BallPromotionEntity(
        id: 1,
        title: "Yangi foydalanuvchi bonusi",
        description: "Ro'yxatdan o'tganingiz uchun 50 ball sovg'a! Birinchi o'yiningizni tanlang va qo'shimcha balllar qo'lga kiriting.",
        ball_reward: 50,
        is_active: true,
        expires_at: DateTime.now().add(const Duration(days: 30)),
      ),
      BallPromotionEntity(
        id: 2,
        title: "Haftalik challenge",
        description: "Haftada 5 ta o'yinda ishtirok eting va 100 ball yutib oling. Barcha sport turlarida ishtirok etishingiz mumkin.",
        ball_reward: 100,
        is_active: true,
        expires_at: DateTime.now().add(const Duration(days: 7)),
      ),
      BallPromotionEntity(
        id: 3,
        title: "Do'stlarni taklif qiling",
        description: "Har bir taklif qilgan do'stingiz uchun 25 ball oling. Do'stlaringiz ham 25 ball oladi!",
        ball_reward: 25,
        is_active: true,
        expires_at: null,
      ),
      BallPromotionEntity(
        id: 4,
        title: "Oylik super bonus",
        description: "Oyda 20 ta o'yinda ishtirok eting va 500 ball yutib oling. Bu eng katta bonusimiz!",
        ball_reward: 500,
        is_active: true,
        expires_at: DateTime.now().add(const Duration(days: 25)),
      ),
      BallPromotionEntity(
        id: 5,
        title: "Tugagan aksiya",
        description: "Bu aksiya muddati tugagan. Yangi aksiyalarni kuzatib boring!",
        ball_reward: 75,
        is_active: false,
        expires_at: DateTime.now().subtract(const Duration(days: 5)),
      ),
    ];

    emit(state.copyWith(
      promotionsStatus: FormzSubmissionStatus.success,
      promotions: mockPromotions,
    ));
  }

  Future<void> _onGetBallPackages(
    GetBallPackagesEvent event,
    Emitter<BallRatingState> emit,
  ) async {
    emit(state.copyWith(packagesStatus: FormzSubmissionStatus.inProgress));

    // Mock data for ball packages
    await Future.delayed(const Duration(milliseconds: 500));

    final mockPackages = [
      BallPackageEntity(
        id: 1,
        ball_amount: 10,
        price_uzs: 15000,
        is_selected: false,
        is_available: true,
      ),
      BallPackageEntity(
        id: 2,
        ball_amount: 25,
        price_uzs: 35000,
        is_selected: false,
        is_available: true,
      ),
      BallPackageEntity(
        id: 3,
        ball_amount: 50,
        price_uzs: 65000,
        is_selected: false,
        is_available: true,
      ),
      BallPackageEntity(
        id: 4,
        ball_amount: 100,
        price_uzs: 120000,
        is_selected: false,
        is_available: true,
      ),
    ];

    emit(state.copyWith(
      packagesStatus: FormzSubmissionStatus.success,
      packages: mockPackages,
    ));
  }

  /// Handles GetCurrentUserBallInfoEvent - fetches current user's ball info from /ball/user-info
  /// This is REAL API data for user's personal ball information
  Future<void> _onGetCurrentUserBallInfo(
    GetCurrentUserBallInfoEvent event,
    Emitter<BallRatingState> emit,
  ) async {
    emit(state.copyWith(userInfoStatus: FormzSubmissionStatus.inProgress));

    final result = await _getCurrentUserBallInfoUseCase(NoParams());

    if (result.isRight) {
      emit(state.copyWith(
        userInfoStatus: FormzSubmissionStatus.success,
        currentUser: result.right,
      ));
    } else {
      emit(state.copyWith(userInfoStatus: FormzSubmissionStatus.failure));
    }
  }

  void _onSelectBallPackage(
    SelectBallPackageEvent event,
    Emitter<BallRatingState> emit,
  ) {
    final updatedPackages = state.packages.map((package) {
      return package.copyWith(is_selected: package.id == event.packageId);
    }).toList();
    
    emit(state.copyWith(
      packages: updatedPackages,
      selectedPackageId: event.packageId,
    ));
  }

  Future<void> _onPurchaseBallPackage(
    PurchaseBallPackageEvent event,
    Emitter<BallRatingState> emit,
  ) async {
    emit(state.copyWith(purchaseStatus: FormzSubmissionStatus.inProgress));

    // Mock purchase process
    await Future.delayed(const Duration(seconds: 2));

    // Simulate successful purchase
    emit(state.copyWith(purchaseStatus: FormzSubmissionStatus.success));
    event.onSuccess?.call();

    // Update user's ball count after purchase
    final selectedPackage = state.packages.firstWhere((p) => p.id == event.packageId);
    final updatedUser = state.currentUser.copyWith(
      total_balls: state.currentUser.total_balls + selectedPackage.ball_amount,
    );

    emit(state.copyWith(currentUser: updatedUser));
  }

  void _onChangeTab(
    ChangeTabEvent event,
    Emitter<BallRatingState> emit,
  ) {
    emit(state.copyWith(currentTabIndex: event.tabIndex));
  }
}
