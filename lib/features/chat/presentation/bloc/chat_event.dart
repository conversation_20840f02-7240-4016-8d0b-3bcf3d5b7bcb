part of 'chat_bloc.dart';

sealed class ChatEvent extends Equatable {
  const ChatEvent();

  @override
  List<Object> get props => [];
}

class GetChatsEvent extends ChatEvent {}

class SendMessageEvent extends ChatEvent {
  final String message;
  const SendMessageEvent({required this.message});

  @override
  List<Object> get props => [message];
}

class AddOptimisticMessageEvent extends ChatEvent {
  final ChatEntity message;
  const AddOptimisticMessageEvent({required this.message});

  @override
  List<Object> get props => [message];
}