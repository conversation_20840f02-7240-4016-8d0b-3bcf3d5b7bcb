import 'package:bloc/bloc.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/chat/domain/entities/chat_entity.dart';
import 'package:echipta/features/chat/domain/usecases/chat_usecase.dart';
import 'package:echipta/features/chat/domain/usecases/send_chat_use_case.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';

part 'chat_event.dart';
part 'chat_state.dart';

class ChatBloc extends Bloc<ChatEvent, ChatState> {
  final ChatUsecase _chatUsecase = ChatUsecase();
  final SendChatUseCase _sendChatUseCase = SendChatUseCase();

  ChatBloc() : super(ChatInitial()) {
    on<GetChatsEvent>(_onGetChats);
    on<SendMessageEvent>(_onSendMessage);
  }

  Future<void> _onGetChats(GetChatsEvent event, Emitter<ChatState> emit) async {
    // Only show loading if we don't have any chats yet
    if (state.chats.isEmpty) {
      emit(state.copyWith(chatStutus: FormzSubmissionStatus.inProgress));
    }

    final result = await _chatUsecase.call(NoParams());
    if (result.isRight) {
      emit(state.copyWith(
        chatStutus: FormzSubmissionStatus.success,
        chats: result.right,
      ));
    } else {
      emit(state.copyWith(chatStutus: FormzSubmissionStatus.failure));
    }
  }

  Future<void> _onSendMessage(SendMessageEvent event, Emitter<ChatState> emit) async {
    emit(state.copyWith(sendMessageStatus: FormzSubmissionStatus.inProgress));

    final params = ChatParams(message: event.message);
    final result = await _sendChatUseCase.call(params);

    if (result.isRight) {
      emit(state.copyWith(sendMessageStatus: FormzSubmissionStatus.success));
      // Refresh chats after successful send
      add(GetChatsEvent());
    } else {
      emit(state.copyWith(sendMessageStatus: FormzSubmissionStatus.failure));
    }
  }


}
