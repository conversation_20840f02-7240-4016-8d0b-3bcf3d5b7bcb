// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:echipta/features/home/<USER>/entities/product_entity.dart';

class CartItemEntity extends Equatable {
  final ProductEntity product;
  final int quantity;
  final String? selectedSize;

  const CartItemEntity({
    required this.product,
    this.quantity = 1,
    this.selectedSize,
  });

  @override
  List<Object?> get props => [product, quantity, selectedSize];

  CartItemEntity copyWith({
    ProductEntity? product,
    int? quantity,
    String? selectedSize,
  }) {
    return CartItemEntity(
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      selectedSize: selectedSize ?? this.selectedSize,
    );
  }

  // Calculate total price for this cart item
  int get totalPrice => product.price * quantity;

  // Convert to API format for basket price calculation
  Map<String, dynamic> toApiJson() {
    final json = <String, dynamic>{
      'product_id': product.id,
      'count': quantity,
    };

    // Add size if selected
    if (selectedSize != null && selectedSize!.isNotEmpty) {
      json['size'] = selectedSize!;
    }

    return json;
  }

  // Helper method to check if product has size options
  bool get hasProductSizes {
    return product.sizes != null &&
           product.sizes is List &&
           (product.sizes as List).isNotEmpty;
  }

  // Helper method to check if size selection is required but not selected
  bool get isSizeRequired {
    return hasProductSizes && (selectedSize == null || selectedSize!.isEmpty);
  }
}
