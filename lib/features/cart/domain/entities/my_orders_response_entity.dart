// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:echipta/features/cart/domain/entities/my_order_entity.dart';

class MyOrdersResponseEntity extends Equatable {
  final List<MyOrderEntity> items;
  final MyOrdersMetaEntity meta;
  final MyOrdersLinksEntity links;

  const MyOrdersResponseEntity({
    this.items = const [],
    this.meta = const MyOrdersMetaEntity(),
    this.links = const MyOrdersLinksEntity(),
  });

  @override
  List<Object?> get props => [items, meta, links];

  MyOrdersResponseEntity copyWith({
    List<MyOrderEntity>? items,
    MyOrdersMetaEntity? meta,
    MyOrdersLinksEntity? links,
  }) {
    return MyOrdersResponseEntity(
      items: items ?? this.items,
      meta: meta ?? this.meta,
      links: links ?? this.links,
    );
  }
}

class MyOrdersMetaEntity extends Equatable {
  final int currentPage;
  final int lastPage;
  final int perPage;
  final int total;

  const MyOrdersMetaEntity({
    this.currentPage = 1,
    this.lastPage = 1,
    this.perPage = 10,
    this.total = 0,
  });

  @override
  List<Object?> get props => [currentPage, lastPage, perPage, total];

  MyOrdersMetaEntity copyWith({
    int? currentPage,
    int? lastPage,
    int? perPage,
    int? total,
  }) {
    return MyOrdersMetaEntity(
      currentPage: currentPage ?? this.currentPage,
      lastPage: lastPage ?? this.lastPage,
      perPage: perPage ?? this.perPage,
      total: total ?? this.total,
    );
  }

  bool get hasNextPage => currentPage < lastPage;
  bool get hasPreviousPage => currentPage > 1;
}

class MyOrdersLinksEntity extends Equatable {
  final String? first;
  final String? last;
  final String? prev;
  final String? next;

  const MyOrdersLinksEntity({
    this.first,
    this.last,
    this.prev,
    this.next,
  });

  @override
  List<Object?> get props => [first, last, prev, next];

  MyOrdersLinksEntity copyWith({
    String? first,
    String? last,
    String? prev,
    String? next,
  }) {
    return MyOrdersLinksEntity(
      first: first ?? this.first,
      last: last ?? this.last,
      prev: prev ?? this.prev,
      next: next ?? this.next,
    );
  }
}
