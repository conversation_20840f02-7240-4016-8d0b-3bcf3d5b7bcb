// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

class BasketPriceEntity extends Equatable {
  final int totalPrice;
  final int deliveryPrice;

  const BasketPriceEntity({
    this.totalPrice = 0,
    this.deliveryPrice = 0,
  });

  @override
  List<Object?> get props => [totalPrice, deliveryPrice];

  BasketPriceEntity copyWith({
    int? totalPrice,
    int? deliveryPrice,
  }) {
    return BasketPriceEntity(
      totalPrice: totalPrice ?? this.totalPrice,
      deliveryPrice: deliveryPrice ?? this.deliveryPrice,
    );
  }

  // Helper getters
  int get grandTotal => totalPrice + deliveryPrice;
}
