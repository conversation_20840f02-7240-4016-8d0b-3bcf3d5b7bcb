// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:echipta/features/home/<USER>/entities/product_entity.dart';

class MyOrderItemEntity extends Equatable {
  final int itemId;
  final ProductEntity product;

  const MyOrderItemEntity({
    this.itemId = 0,
    this.product = const ProductEntity(),
  });

  @override
  List<Object?> get props => [itemId, product];

  MyOrderItemEntity copyWith({
    int? itemId,
    ProductEntity? product,
  }) {
    return MyOrderItemEntity(
      itemId: itemId ?? this.itemId,
      product: product ?? this.product,
    );
  }
}
