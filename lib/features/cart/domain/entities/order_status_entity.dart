// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

class OrderStatusEntity extends Equatable {
  final int status;
  final String info;

  const OrderStatusEntity({
    this.status = 0,
    this.info = '',
  });

  @override
  List<Object?> get props => [status, info];

  OrderStatusEntity copyWith({
    int? status,
    String? info,
  }) {
    return OrderStatusEntity(
      status: status ?? this.status,
      info: info ?? this.info,
    );
  }

  // Helper method to get status display text
  String get statusText {
    switch (status) {
      case 0:
        return 'Pending';
      case 1:
        return 'Processing';
      case 2:
        return 'Successfully paid';
      case 3:
        return 'Delivered';
      case -1:
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  }

  // Helper method to check if order is completed
  bool get isCompleted => status == 2 || status == 3;

  // Helper method to check if order is pending
  bool get isPending => status == 0 || status == 1;

  // Helper method to check if order is cancelled
  bool get isCancelled => status == -1;
}
