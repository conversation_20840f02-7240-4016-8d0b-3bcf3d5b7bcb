// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:echipta/features/cart/domain/entities/my_order_item_entity.dart';

class MyOrderEntity extends Equatable {
  final int id;
  final int type;
  final int status;
  final String? paymentUrl;
  final bool isGifted;
  final int amount;
  final dynamic ticket; // Can be null or ticket data
  final List<MyOrderItemEntity> items;

  const MyOrderEntity({
    this.id = 0,
    this.type = 0,
    this.status = 0,
    this.paymentUrl,
    this.isGifted = false,
    this.amount = 0,
    this.ticket,
    this.items = const [],
  });

  @override
  List<Object?> get props => [
        id,
        type,
        status,
        paymentUrl,
        isGifted,
        amount,
        ticket,
        items,
      ];

  MyOrderEntity copyWith({
    int? id,
    int? type,
    int? status,
    String? paymentUrl,
    bool? isGifted,
    int? amount,
    dynamic ticket,
    List<MyOrderItemEntity>? items,
  }) {
    return MyOrderEntity(
      id: id ?? this.id,
      type: type ?? this.type,
      status: status ?? this.status,
      paymentUrl: paymentUrl ?? this.paymentUrl,
      isGifted: isGifted ?? this.isGifted,
      amount: amount ?? this.amount,
      ticket: ticket ?? this.ticket,
      items: items ?? this.items,
    );
  }

  // Helper methods for status
  bool get isPending => status == 1;
  bool get isCompleted => status == 2;
  bool get isCancelled => status == 3;

  String get statusText {
    switch (status) {
      case 1:
        return 'Kutilmoqda';
      case 2:
        return 'Yakunlangan';
      case 3:
        return 'Bekor qilingan';
      default:
        return 'Noma\'lum';
    }
  }

  // Helper methods for type
  bool get isTicketOrder => type == 1;
  bool get isProductOrder => type == 2;

  String get typeText {
    switch (type) {
      case 1:
        return 'Chipta';
      case 2:
        return 'Mahsulot';
      default:
        return 'Noma\'lum';
    }
  }
}
