import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/cart/data/repository/cart_repository_impl.dart';
import 'package:echipta/features/cart/domain/entities/cart_item_entity.dart';
import 'package:echipta/features/cart/domain/entities/order_entity.dart';
import 'package:echipta/features/cart/domain/repository/cart_repository.dart';

class CreateOrderParams {
  final List<CartItemEntity> items;
  final DeliveryType deliveryType;
  final String? deliveryAddress;
  final PaymentType paymentType;

  CreateOrderParams({
    required this.items,
    required this.deliveryType,
    this.deliveryAddress,
    required this.paymentType,
  });
}

class CreateOrderUseCase extends UseCase<OrderEntity, CreateOrderParams> {
  final CartRepository _repository = serviceLocator<CartRepositoryImpl>();

  @override
  Future<Either<Failure, OrderEntity>> call(CreateOrderParams params) async {
    return await _repository.createOrder(
      params.items,
      params.deliveryType,
      params.deliveryAddress,
      params.paymentType,
    );
  }
}
