import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/cart/data/repository/cart_repository_impl.dart';
import 'package:echipta/features/cart/domain/entities/basket_price_entity.dart';
import 'package:echipta/features/cart/domain/entities/cart_item_entity.dart';
import 'package:echipta/features/cart/domain/entities/order_entity.dart';
import 'package:echipta/features/cart/domain/repository/cart_repository.dart';

class GetBasketPriceParams {
  final List<CartItemEntity> items;
  final DeliveryType deliveryType;

  GetBasketPriceParams({
    required this.items,
    required this.deliveryType,
  });
}

class GetBasketPriceUseCase extends UseCase<BasketPriceEntity, GetBasketPriceParams> {
  final CartRepository _repository = serviceLocator<CartRepositoryImpl>();

  @override
  Future<Either<Failure, BasketPriceEntity>> call(GetBasketPriceParams params) async {
    return await _repository.getBasketPrice(params.items, params.deliveryType);
  }
}
