import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/cart/data/repository/cart_repository_impl.dart';
import 'package:echipta/features/cart/domain/entities/order_status_entity.dart';
import 'package:echipta/features/cart/domain/repository/cart_repository.dart';

class GetOrderStatusUseCase extends UseCase<OrderStatusEntity, IdParam> {
  final CartRepository _repository = serviceLocator<CartRepositoryImpl>();

  @override
  Future<Either<Failure, OrderStatusEntity>> call(IdParam params) async {
    return await _repository.getOrderStatus(params.id ?? 0);
  }
}
