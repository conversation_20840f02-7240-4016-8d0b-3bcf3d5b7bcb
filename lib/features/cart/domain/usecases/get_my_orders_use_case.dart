import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/cart/data/repository/cart_repository_impl.dart';
import 'package:echipta/features/cart/domain/entities/my_orders_response_entity.dart';
import 'package:echipta/features/cart/domain/repository/cart_repository.dart';

class GetMyOrdersParams {
  final int page;

  GetMyOrdersParams({
    this.page = 1,
  });
}

class GetMyOrdersUseCase extends UseCase<MyOrdersResponseEntity, GetMyOrdersParams> {
  final CartRepository _repository = serviceLocator<CartRepositoryImpl>();

  @override
  Future<Either<Failure, MyOrdersResponseEntity>> call(GetMyOrdersParams params) async {
    return await _repository.getMyOrders(page: params.page);
  }
}
