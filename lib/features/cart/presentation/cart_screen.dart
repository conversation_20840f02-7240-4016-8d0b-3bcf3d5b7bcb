import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/cart/presentation/widgets/w_cart_tab.dart';
import 'package:echipta/features/cart/presentation/widgets/w_orders_tab.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';

import 'bloc/cart_bloc.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  int? _lastHandledOrderId; // Track the last order we handled
  bool _isProcessingOrder = false; // Track if we're currently processing an order

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    // Reset processing flag when screen initializes
    _isProcessingOrder = false;
    // Refresh user balance when cart screen loads
    context.read<ProfileBloc>().add(GetMeEvent());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void switchToOrdersTab() {
    _tabController.animateTo(1);
    // Refresh orders data
    context.read<CartBloc>().add(const GetMyOrders(page: 1));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CartBloc, CartState>(
      listener: (context, state) {
        // Track when order processing starts
        if (state.orderStatus == FormzSubmissionStatus.inProgress) {
          _isProcessingOrder = true;
        }

        // Listen for successful order creation and switch to orders tab
        // Only trigger when:
        // 1. Order status is success
        // 2. We have a valid current order
        // 3. We haven't already handled this specific order
        // 4. We were processing an order (to ensure this is a fresh order)
        if (state.orderStatus == FormzSubmissionStatus.success &&
            state.currentOrder != null &&
            _lastHandledOrderId != state.currentOrder!.orderId &&
            _isProcessingOrder) {

          // Mark this order as handled and reset processing flag
          _lastHandledOrderId = state.currentOrder!.orderId;
          _isProcessingOrder = false;

          // Add a small delay for better UX
          Future.delayed(const Duration(milliseconds: 1000), () {
            if (mounted) {
              switchToOrdersTab();
            }
          });
        }

        // Reset processing flag on failure
        if (state.orderStatus == FormzSubmissionStatus.failure) {
          _isProcessingOrder = false;
        }
      },
      child: Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        title: const Text(
          'Savat',
          style: TextStyle(
            color: AppColors.dark,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          labelStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w400,
          ),
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.grey,
          indicatorColor: AppColors.primary,
          indicatorWeight: 2,
          dividerHeight: 0,
          tabs: const [
            Tab(text: "Savat"),
            Tab(text: "Buyurtmalar"),
          ],
        ),
      ),
      body: Padding(
        padding: EdgeInsets.only(bottom: context.padding.bottom),
        child: TabBarView(
          controller: _tabController,
          children: [
            WCartTab(onSwitchToOrders: switchToOrdersTab),
            const WOrdersTab(),
          ],
        ),
      ),
      ),
    );
  }
}
