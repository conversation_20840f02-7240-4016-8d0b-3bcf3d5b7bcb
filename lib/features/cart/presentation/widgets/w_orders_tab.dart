import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/features/cart/presentation/bloc/cart_bloc.dart';
import 'package:echipta/features/cart/presentation/widgets/w_my_order_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:formz/formz.dart';

class WOrdersTab extends StatefulWidget {
  const WOrdersTab({super.key});

  @override
  State<WOrdersTab> createState() => _WOrdersTabState();
}

class _WOrdersTabState extends State<WOrdersTab> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Load orders when the tab is first opened
    context.read<CartBloc>().add(const GetMyOrders());

    // Add scroll listener for pagination
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8) {
      context.read<CartBloc>().add(const LoadMoreMyOrders());
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, state) {
        // Loading state
        if (state.myOrdersStatus == FormzSubmissionStatus.inProgress && state.myOrders.isEmpty) {
          return const Center(
            child: CircularProgressIndicator(color: AppColors.primary),
          );
        }

        // Error state
        if (state.myOrdersStatus == FormzSubmissionStatus.failure && state.myOrders.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 80,
                  color: AppColors.grey,
                ),
                const Gap(16),
                const Text(
                  'Xatolik yuz berdi',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.grey,
                  ),
                ),
                const Gap(8),
                const Text(
                  'Buyurtmalarni yuklashda xatolik',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.grey,
                  ),
                ),
                const Gap(16),
                ElevatedButton(
                  onPressed: () {
                    context.read<CartBloc>().add(const GetMyOrders());
                  },
                  child: const Text('Qayta urinish'),
                ),
              ],
            ),
          );
        }

        // Empty state
        if (state.myOrders.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.receipt_long_outlined,
                  size: 80,
                  color: AppColors.grey,
                ),
                Gap(16),
                Text(
                  'Buyurtmalar yo\'q',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.grey,
                  ),
                ),
                Gap(8),
                Text(
                  'Hali hech qanday buyurtma\nbermagansiz',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        // Orders list
        return RefreshIndicator(
          onRefresh: () async {
            context.read<CartBloc>().add(const GetMyOrders());
          },
          child: ListView.separated(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            itemCount: state.myOrders.length + (state.isLoadingMoreOrders ? 1 : 0),
            separatorBuilder: (context, index) => const Gap(12),
            itemBuilder: (context, index) {
              if (index >= state.myOrders.length) {
                // Loading indicator for pagination
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: CircularProgressIndicator(color: AppColors.primary),
                  ),
                );
              }

              final order = state.myOrders[index];
              return WMyOrderItem(order: order);
            },
          ),
        );
      },
    );
  }
}
