import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/cart/presentation/bloc/cart_bloc.dart';
import 'package:echipta/features/cart/presentation/widgets/w_cart_item.dart';
import 'package:echipta/features/cart/presentation/widgets/w_delivery_options.dart';
import 'package:echipta/features/cart/presentation/widgets/w_cart_summary.dart';
import 'package:echipta/features/cart/presentation/widgets/w_cart_payment.dart';
import 'package:echipta/features/cart/presentation/widgets/w_payment_handler.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/common/widgets/w_error_modal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:formz/formz.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:go_router/go_router.dart';

import '../../domain/entities/order_entity.dart';

class WCartTab extends StatefulWidget {
  final VoidCallback? onSwitchToOrders;

  const WCartTab({super.key, this.onSwitchToOrders});

  @override
  State<WCartTab> createState() => _WCartTabState();
}

class _WCartTabState extends State<WCartTab> {
  int? _processedOrderId; // Track processed orders to prevent infinite loop

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, state) {
        if (state.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.shopping_cart_outlined,
                  size: 80,
                  color: AppColors.grey,
                ),
                Gap(16),
                Text(
                  'Savat bo\'sh',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.grey,
                  ),
                ),
                Gap(8),
                Text(
                  'Mahsulotlarni qo\'shish uchun\nbosh sahifaga o\'ting',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14, color: AppColors.grey),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Cart Items
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.cartItems.length,
                      separatorBuilder: (context, index) => const Gap(12),
                      itemBuilder: (context, index) {
                        final item = state.cartItems[index];
                        return WCartItem(item: item);
                      },
                    ),
                    const Gap(24),

                    // Delivery Options
                    const WDeliveryOptions(),
                    const Gap(24),

                    // Payment Options
                    const WCartPayment(),
                    const Gap(24),

                    // Cart Summary
                    const WCartSummary(),
                  ],
                ),
              ),
            ),

            // Bottom Action Button
            Padding(
              padding: EdgeInsets.symmetric(vertical: 10),
              child: BlocListener<CartBloc, CartState>(
                listener: (context, state) {
                  if (state.orderStatus == FormzSubmissionStatus.success &&
                      state.currentOrder != null &&
                      _processedOrderId != state.currentOrder!.orderId) {
                    // Mark this order as processed to prevent infinite loop
                    _processedOrderId = state.currentOrder!.orderId;

                    // Add order to history
                    context.read<CartBloc>().add(
                      AddOrderToHistory(order: state.currentOrder!),
                    );

                    // Handle different payment types
                    if (state.paymentType == PaymentType.balance &&
                        state.currentOrder?.orderId != 0) {
                      // For balance payments with valid order ID, navigate to awaiting screen
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder:
                              (context) => WPaymentAwaitingScreen(
                                orderId: state.currentOrder!.orderId,
                                paymentType: state.paymentType,
                                onSwitchToOrders: widget.onSwitchToOrders,
                              ),
                        ),
                      );
                    } else if (state.paymentType == PaymentType.balance &&
                        state.currentOrder?.orderId == 0) {
                      // For successful balance payments (orderId: 0), show success and clear cart
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text(
                            'To\'lov muvaffaqiyatli amalga oshirildi!',
                            style: TextStyle(color: AppColors.white),
                          ),
                          backgroundColor: AppColors.success,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      );
                      // Clear cart and switch to orders tab
                      context.read<CartBloc>().add(const ClearCart());
                      // Switch to orders tab after a short delay
                      Future.delayed(const Duration(seconds: 1), () {
                        if (context.mounted && widget.onSwitchToOrders != null) {
                          widget.onSwitchToOrders!();
                        }
                      });
                    } else if (state.currentOrder?.paymentUrl != null) {
                      // For Alif payments, use URL launcher directly (like order screen)
                      _handleAlifPayment(
                        context,
                        state.currentOrder!.paymentUrl!,
                        state.currentOrder!.orderId,
                        widget.onSwitchToOrders,
                      );
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Buyurtma muvaffaqiyatli yaratildi!'),
                          backgroundColor: AppColors.success,
                        ),
                      );
                      // Clear cart for pickup orders
                      context.read<CartBloc>().add(const ClearCart());
                      // Switch to orders tab after a short delay
                      Future.delayed(const Duration(seconds: 1), () {
                        if (context.mounted && widget.onSwitchToOrders != null) {
                          widget.onSwitchToOrders!();
                        }
                      });
                    }
                  } else if (state.orderStatus ==
                      FormzSubmissionStatus.failure) {
                    // Handle specific error messages
                    String errorMessage =
                        'Buyurtma yaratishda xatolik yuz berdi';

                    if (state.orderErrorMessage != null) {
                      if (state.orderErrorMessage == "Balance not enough") {
                        errorMessage = "Balansingizda yetarli mablag' yo'q!";
                      } else {
                        errorMessage = state.orderErrorMessage!;
                      }
                    }

                    // Show error modal instead of snackbar for better UX
                    showModalBottomSheet(
                      context: context,
                      backgroundColor: AppColors.white,
                      elevation: 0,
                      builder: (context) {
                        return WErrorModal(title: errorMessage);
                      },
                    );
                  }
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: WButton(
                    btnColor:
                        state.canCreateOrder
                            ? AppColors.primary
                            : AppColors.lightGrey,
                    onTap:
                        state.canCreateOrder
                            ? () {
                              // Reset order status before creating new order to ensure BlocListener triggers
                              if (state.orderStatus ==
                                  FormzSubmissionStatus.failure) {
                                context.read<CartBloc>().add(
                                  const ResetOrderStatus(),
                                );
                              }
                              context.read<CartBloc>().add(const CreateOrder());
                            }
                            : () {
                              //Show snack
                              context.showCustomSnackbar(
                                "Manzilni kiriting...",
                              );
                            },
                    isLoading:
                        state.orderStatus == FormzSubmissionStatus.inProgress,
                    txt: 'Buyurtma berish',
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

// Handle Alif payment using URL launcher (same as order screen)
Future<void> _handleAlifPayment(
  BuildContext context,
  String paymentUrl,
  int orderId,
  VoidCallback? onSwitchToOrders,
) async {
  try {
    final Uri url = Uri.parse(paymentUrl);
    print("🔍 Parsed URL: $url");

    // Try different launch modes for better compatibility (same as order screen)
    bool launched = false;

    // First try: InAppWebView
    try {
      launched = await launchUrl(url, mode: LaunchMode.inAppWebView);
      print("✅ URL launched with InAppWebView: $launched");
    } catch (e) {
      print("❌ InAppWebView failed: $e");
    }

    // Second try: External browser if InAppWebView failed
    if (!launched) {
      try {
        launched = await launchUrl(url, mode: LaunchMode.externalApplication);
        print("✅ URL launched with external browser: $launched");
      } catch (e) {
        print("❌ External browser failed: $e");
      }
    }

    // Third try: Platform default
    if (!launched) {
      try {
        launched = await launchUrl(url);
        print("✅ URL launched with platform default: $launched");
      } catch (e) {
        print("❌ Platform default failed: $e");
      }
    }

    if (launched) {
      print(
        "✅ URL launched successfully, will navigate to status after user returns",
      );
      // Wait a bit for the webview to open, then navigate to status
      Future.delayed(Duration(seconds: 2), () {
        if (context.mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (context) => WPaymentAwaitingScreen(
                    orderId: orderId,
                    paymentType: PaymentType.alif,
                    onSwitchToOrders: onSwitchToOrders,
                  ),
            ),
          );
        }
      });
    } else {
      print("❌ All URL launch methods failed");
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("To'lov sahifasini ochishda xatolik yuz berdi"),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  } catch (e) {
    print("❌ URL parsing failed: $e");
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Noto'g'ri to'lov havolasi"),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
