import 'package:bloc/bloc.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/cart/domain/entities/basket_price_entity.dart';
import 'package:echipta/features/cart/domain/entities/cart_item_entity.dart';
import 'package:echipta/features/cart/domain/entities/order_entity.dart';
import 'package:echipta/features/cart/domain/entities/order_status_entity.dart';
import 'package:echipta/features/cart/domain/entities/my_order_entity.dart';
import 'package:echipta/features/cart/domain/entities/my_orders_response_entity.dart';
import 'package:echipta/features/cart/domain/usecases/create_order_use_case.dart';
import 'package:echipta/features/cart/domain/usecases/get_basket_price_use_case.dart';
import 'package:echipta/features/cart/domain/usecases/get_order_status_use_case.dart';
import 'package:echipta/features/cart/domain/usecases/get_my_orders_use_case.dart';
import 'package:echipta/features/home/<USER>/entities/product_entity.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';

part 'cart_event.dart';
part 'cart_state.dart';

class CartBloc extends Bloc<CartEvent, CartState> {
  final GetBasketPriceUseCase _getBasketPriceUseCase = GetBasketPriceUseCase();
  final CreateOrderUseCase _createOrderUseCase = CreateOrderUseCase();
  final GetOrderStatusUseCase _getOrderStatusUseCase = GetOrderStatusUseCase();
  final GetMyOrdersUseCase _getMyOrdersUseCase = GetMyOrdersUseCase();

  CartBloc() : super(const CartState()) {
    on<AddToCart>(_onAddToCart);
    on<RemoveFromCart>(_onRemoveFromCart);
    on<UpdateQuantity>(_onUpdateQuantity);
    on<ClearCart>(_onClearCart);
    on<SetDeliveryType>(_onSetDeliveryType);
    on<SetDeliveryAddress>(_onSetDeliveryAddress);
    on<SetPaymentType>(_onSetPaymentType);
    on<GetBasketPrice>(_onGetBasketPrice);
    on<CreateOrder>(_onCreateOrder);
    on<GetOrderStatus>(_onGetOrderStatus);
    on<AddOrderToHistory>(_onAddOrderToHistory);
    on<ResetOrderStatus>(_onResetOrderStatus);
    on<GetMyOrders>(_onGetMyOrders);
    on<LoadMoreMyOrders>(_onLoadMoreMyOrders);
    on<UpdateCartItemSize>(_onUpdateCartItemSize);
  }

  void _onAddToCart(AddToCart event, Emitter<CartState> emit) {
    final List<CartItemEntity> updatedItems = List.from(state.cartItems);

    // Reset order state when starting a new cart session
    // This prevents false triggers from previous successful orders
    bool shouldResetOrderState = state.orderStatus == FormzSubmissionStatus.success;

    // Check if product already exists in cart with same size
    final existingIndex = updatedItems.indexWhere((item) =>
      item.product.id == event.product.id &&
      item.selectedSize == event.selectedSize);

    if (existingIndex != -1) {
      // Update quantity if product with same size exists
      final existingItem = updatedItems[existingIndex];
      updatedItems[existingIndex] = existingItem.copyWith(
        quantity: existingItem.quantity + (event.quantity ?? 1),
      );
    } else {
      // Add new item to cart
      updatedItems.add(CartItemEntity(
        product: event.product,
        quantity: event.quantity ?? 1,
        selectedSize: event.selectedSize,
      ));
    }

    emit(state.copyWith(
      cartItems: updatedItems,
      // Reset order state if we had a previous successful order
      orderStatus: shouldResetOrderState ? FormzSubmissionStatus.initial : state.orderStatus,
      currentOrder: shouldResetOrderState ? null : state.currentOrder,
      clearOrderErrorMessage: shouldResetOrderState,
    ));

    // Trigger basket price calculation if cart is not empty
    if (updatedItems.isNotEmpty) {
      add(const GetBasketPrice());
    }
  }

  void _onRemoveFromCart(RemoveFromCart event, Emitter<CartState> emit) {
    final List<CartItemEntity> updatedItems = List.from(state.cartItems);

    // Remove specific product with specific size, or all sizes if no size specified
    updatedItems.removeWhere((item) =>
      item.product.id == event.productId &&
      (event.selectedSize == null || item.selectedSize == event.selectedSize));

    emit(state.copyWith(cartItems: updatedItems));

    // Trigger basket price calculation if cart is not empty
    if (updatedItems.isNotEmpty) {
      add(const GetBasketPrice());
    }
  }

  void _onUpdateQuantity(UpdateQuantity event, Emitter<CartState> emit) {
    final List<CartItemEntity> updatedItems = List.from(state.cartItems);

    // Find specific product with specific size, or first match if no size specified
    final index = updatedItems.indexWhere((item) =>
      item.product.id == event.productId &&
      (event.selectedSize == null || item.selectedSize == event.selectedSize));

    if (index != -1) {
      if (event.quantity <= 0) {
        updatedItems.removeAt(index);
      } else {
        updatedItems[index] = updatedItems[index].copyWith(quantity: event.quantity);
      }
    }

    emit(state.copyWith(cartItems: updatedItems));

    // Trigger basket price calculation if cart is not empty
    if (updatedItems.isNotEmpty) {
      add(const GetBasketPrice());
    }
  }

  void _onClearCart(ClearCart event, Emitter<CartState> emit) {
    emit(state.copyWith(
      cartItems: [],
      // Reset order-related state to prevent false triggers
      orderStatus: FormzSubmissionStatus.initial,
      currentOrder: null,
      basketPrice: null,
      basketPriceStatus: FormzSubmissionStatus.initial,
      clearOrderErrorMessage: true,
    ));
  }

  void _onSetDeliveryType(SetDeliveryType event, Emitter<CartState> emit) {
    emit(state.copyWith(
      deliveryType: event.deliveryType,
      orderStatus: FormzSubmissionStatus.initial, // Reset order status when delivery type changes
      clearOrderErrorMessage: true, // Clear any previous error messages
    ));

    // Trigger basket price calculation when delivery type changes and cart is not empty
    if (state.cartItems.isNotEmpty) {
      add(const GetBasketPrice());
    }
  }

  void _onSetDeliveryAddress(SetDeliveryAddress event, Emitter<CartState> emit) {
    emit(state.copyWith(
      deliveryAddress: event.address,
      orderStatus: FormzSubmissionStatus.initial, // Reset order status when delivery address changes
      clearOrderErrorMessage: true, // Clear any previous error messages
    ));
  }

  void _onSetPaymentType(SetPaymentType event, Emitter<CartState> emit) {
    emit(state.copyWith(
      paymentType: event.paymentType,
      orderStatus: FormzSubmissionStatus.initial, // Reset order status when payment type changes
      clearOrderErrorMessage: true, // Clear any previous error messages
    ));
  }

  void _onGetBasketPrice(GetBasketPrice event, Emitter<CartState> emit) async {
    if (state.cartItems.isEmpty) return;
    
    emit(state.copyWith(basketPriceStatus: FormzSubmissionStatus.inProgress));
    
    final result = await _getBasketPriceUseCase(GetBasketPriceParams(
      items: state.cartItems,
      deliveryType: state.deliveryType,
    ));
    
    if (result.isRight) {
      emit(state.copyWith(
        basketPrice: result.right,
        basketPriceStatus: FormzSubmissionStatus.success,
      ));
    } else {
      emit(state.copyWith(basketPriceStatus: FormzSubmissionStatus.failure));
    }
  }

  void _onCreateOrder(CreateOrder event, Emitter<CartState> emit) async {
    if (state.cartItems.isEmpty) return;

    emit(state.copyWith(
      orderStatus: FormzSubmissionStatus.inProgress,
      clearOrderErrorMessage: true, // Clear previous error
    ));

    final result = await _createOrderUseCase(CreateOrderParams(
      items: state.cartItems,
      deliveryType: state.deliveryType,
      deliveryAddress: state.deliveryAddress,
      paymentType: state.paymentType,
    ));

    if (result.isRight) {
      emit(state.copyWith(
        currentOrder: result.right,
        orderStatus: FormzSubmissionStatus.success,
        clearOrderErrorMessage: true, // Clear error on success
      ));
    } else {
      emit(state.copyWith(
        orderStatus: FormzSubmissionStatus.failure,
        orderErrorMessage: result.left.errorMessage,
      ));
    }
  }

  void _onGetOrderStatus(GetOrderStatus event, Emitter<CartState> emit) async {
    emit(state.copyWith(orderStatusCheckStatus: FormzSubmissionStatus.inProgress));
    
    final result = await _getOrderStatusUseCase(IdParam(id: event.orderId));
    
    if (result.isRight) {
      emit(state.copyWith(
        currentOrderStatus: result.right,
        orderStatusCheckStatus: FormzSubmissionStatus.success,
      ));
    } else {
      emit(state.copyWith(orderStatusCheckStatus: FormzSubmissionStatus.failure));
    }
  }

  void _onAddOrderToHistory(AddOrderToHistory event, Emitter<CartState> emit) {
    final List<OrderEntity> updatedHistory = List.from(state.orderHistory);
    updatedHistory.insert(0, event.order);

    emit(state.copyWith(orderHistory: updatedHistory));
  }

  void _onResetOrderStatus(ResetOrderStatus event, Emitter<CartState> emit) {
    emit(state.copyWith(
      orderStatus: FormzSubmissionStatus.initial,
      clearOrderErrorMessage: true,
    ));
  }

  void _onGetMyOrders(GetMyOrders event, Emitter<CartState> emit) async {
    emit(state.copyWith(myOrdersStatus: FormzSubmissionStatus.inProgress));

    final result = await _getMyOrdersUseCase(GetMyOrdersParams(page: event.page));

    if (result.isRight) {
      final response = result.right;
      emit(state.copyWith(
        myOrders: response.items,
        myOrdersMeta: response.meta,
        myOrdersStatus: FormzSubmissionStatus.success,
      ));
    } else {
      emit(state.copyWith(myOrdersStatus: FormzSubmissionStatus.failure));
    }
  }

  void _onLoadMoreMyOrders(LoadMoreMyOrders event, Emitter<CartState> emit) async {
    if (state.myOrdersMeta == null || !state.myOrdersMeta!.hasNextPage || state.isLoadingMoreOrders) {
      return;
    }

    emit(state.copyWith(isLoadingMoreOrders: true));

    final nextPage = state.myOrdersMeta!.currentPage + 1;
    final result = await _getMyOrdersUseCase(GetMyOrdersParams(page: nextPage));

    if (result.isRight) {
      final response = result.right;
      final updatedOrders = List<MyOrderEntity>.from(state.myOrders)..addAll(response.items);

      emit(state.copyWith(
        myOrders: updatedOrders,
        myOrdersMeta: response.meta,
        isLoadingMoreOrders: false,
      ));
    } else {
      emit(state.copyWith(isLoadingMoreOrders: false));
    }
  }

  void _onUpdateCartItemSize(UpdateCartItemSize event, Emitter<CartState> emit) {
    final List<CartItemEntity> updatedItems = List.from(state.cartItems);

    // Find the item to update
    final itemIndex = updatedItems.indexWhere((item) => item.product.id == event.productId);

    if (itemIndex != -1) {
      // Update the size of the item
      updatedItems[itemIndex] = updatedItems[itemIndex].copyWith(
        selectedSize: event.size,
      );

      emit(state.copyWith(cartItems: updatedItems));

      // Trigger basket price calculation
      if (updatedItems.isNotEmpty) {
        add(const GetBasketPrice());
      }
    }
  }
}
