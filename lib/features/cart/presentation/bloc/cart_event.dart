part of 'cart_bloc.dart';

abstract class CartEvent extends Equatable {
  const CartEvent();

  @override
  List<Object?> get props => [];
}

class AddToCart extends CartEvent {
  final ProductEntity product;
  final int? quantity;
  final String? selectedSize;

  const AddToCart({required this.product, this.quantity, this.selectedSize});

  @override
  List<Object?> get props => [product, quantity, selectedSize];
}

class RemoveFromCart extends CartEvent {
  final int productId;
  final String? selectedSize;

  const RemoveFromCart({required this.productId, this.selectedSize});

  @override
  List<Object?> get props => [productId, selectedSize];
}

class UpdateQuantity extends CartEvent {
  final int productId;
  final int quantity;
  final String? selectedSize;

  const UpdateQuantity({required this.productId, required this.quantity, this.selectedSize});

  @override
  List<Object?> get props => [productId, quantity, selectedSize];
}

class ClearCart extends CartEvent {
  const ClearCart();
}

class SetDeliveryType extends CartEvent {
  final DeliveryType deliveryType;

  const SetDeliveryType({required this.deliveryType});

  @override
  List<Object?> get props => [deliveryType];
}

class SetDeliveryAddress extends CartEvent {
  final String address;

  const SetDeliveryAddress({required this.address});

  @override
  List<Object?> get props => [address];
}

class SetPaymentType extends CartEvent {
  final PaymentType paymentType;

  const SetPaymentType({required this.paymentType});

  @override
  List<Object?> get props => [paymentType];
}

class GetBasketPrice extends CartEvent {
  const GetBasketPrice();
}

class CreateOrder extends CartEvent {
  const CreateOrder();
}

class GetOrderStatus extends CartEvent {
  final int orderId;

  const GetOrderStatus({required this.orderId});

  @override
  List<Object?> get props => [orderId];
}

class AddOrderToHistory extends CartEvent {
  final OrderEntity order;

  const AddOrderToHistory({required this.order});

  @override
  List<Object?> get props => [order];
}

class ResetOrderStatus extends CartEvent {
  const ResetOrderStatus();
}

class GetMyOrders extends CartEvent {
  final int page;

  const GetMyOrders({this.page = 1});

  @override
  List<Object?> get props => [page];
}

class LoadMoreMyOrders extends CartEvent {
  const LoadMoreMyOrders();
}

class UpdateCartItemSize extends CartEvent {
  final int productId;
  final String size;

  const UpdateCartItemSize({required this.productId, required this.size});

  @override
  List<Object?> get props => [productId, size];
}
