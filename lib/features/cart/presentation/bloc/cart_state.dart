part of 'cart_bloc.dart';

class CartState extends Equatable {
  final List<CartItemEntity> cartItems;
  final DeliveryType deliveryType;
  final PaymentType paymentType;
  final String? deliveryAddress;
  final BasketPriceEntity? basketPrice;
  final OrderEntity? currentOrder;
  final OrderStatusEntity? currentOrderStatus;
  final List<OrderEntity> orderHistory;
  final FormzSubmissionStatus basketPriceStatus;
  final FormzSubmissionStatus orderStatus;
  final FormzSubmissionStatus orderStatusCheckStatus;
  final String? orderErrorMessage;
  // My Orders API fields
  final List<MyOrderEntity> myOrders;
  final MyOrdersMetaEntity? myOrdersMeta;
  final FormzSubmissionStatus myOrdersStatus;
  final bool isLoadingMoreOrders;

  const CartState({
    this.cartItems = const [],
    this.deliveryType = DeliveryType.inStore,
    this.paymentType = PaymentType.alif,
    this.deliveryAddress,
    this.basketPrice,
    this.currentOrder,
    this.currentOrderStatus,
    this.orderHistory = const [],
    this.basketPriceStatus = FormzSubmissionStatus.initial,
    this.orderStatus = FormzSubmissionStatus.initial,
    this.orderStatusCheckStatus = FormzSubmissionStatus.initial,
    this.orderErrorMessage,
    this.myOrders = const [],
    this.myOrdersMeta,
    this.myOrdersStatus = FormzSubmissionStatus.initial,
    this.isLoadingMoreOrders = false,
  });

  @override
  List<Object?> get props => [
        cartItems,
        deliveryType,
        paymentType,
        deliveryAddress,
        basketPrice,
        currentOrder,
        currentOrderStatus,
        orderHistory,
        basketPriceStatus,
        orderStatus,
        orderStatusCheckStatus,
        orderErrorMessage,
        myOrders,
        myOrdersMeta,
        myOrdersStatus,
        isLoadingMoreOrders,
      ];

  CartState copyWith({
    List<CartItemEntity>? cartItems,
    DeliveryType? deliveryType,
    PaymentType? paymentType,
    String? deliveryAddress,
    BasketPriceEntity? basketPrice,
    OrderEntity? currentOrder,
    OrderStatusEntity? currentOrderStatus,
    List<OrderEntity>? orderHistory,
    FormzSubmissionStatus? basketPriceStatus,
    FormzSubmissionStatus? orderStatus,
    FormzSubmissionStatus? orderStatusCheckStatus,
    String? orderErrorMessage,
    bool clearOrderErrorMessage = false,
    List<MyOrderEntity>? myOrders,
    MyOrdersMetaEntity? myOrdersMeta,
    FormzSubmissionStatus? myOrdersStatus,
    bool? isLoadingMoreOrders,
  }) {
    return CartState(
      cartItems: cartItems ?? this.cartItems,
      deliveryType: deliveryType ?? this.deliveryType,
      paymentType: paymentType ?? this.paymentType,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      basketPrice: basketPrice ?? this.basketPrice,
      currentOrder: currentOrder ?? this.currentOrder,
      currentOrderStatus: currentOrderStatus ?? this.currentOrderStatus,
      orderHistory: orderHistory ?? this.orderHistory,
      basketPriceStatus: basketPriceStatus ?? this.basketPriceStatus,
      orderStatus: orderStatus ?? this.orderStatus,
      orderStatusCheckStatus: orderStatusCheckStatus ?? this.orderStatusCheckStatus,
      orderErrorMessage: clearOrderErrorMessage ? null : (orderErrorMessage ?? this.orderErrorMessage),
      myOrders: myOrders ?? this.myOrders,
      myOrdersMeta: myOrdersMeta ?? this.myOrdersMeta,
      myOrdersStatus: myOrdersStatus ?? this.myOrdersStatus,
      isLoadingMoreOrders: isLoadingMoreOrders ?? this.isLoadingMoreOrders,
    );
  }

  // Helper getters
  int get totalItems => cartItems.fold(0, (sum, item) => sum + item.quantity);
  
  int get totalPrice => cartItems.fold(0, (sum, item) => sum + item.totalPrice);
  
  bool get isEmpty => cartItems.isEmpty;
  
  bool get isNotEmpty => cartItems.isNotEmpty;
  
  bool get requiresDeliveryAddress => deliveryType == DeliveryType.delivery;
  
  bool get canCreateOrder => isNotEmpty &&
                             (!requiresDeliveryAddress || (deliveryAddress?.isNotEmpty ?? false)) &&
                             !hasItemsWithMissingSizes;

  // Check if any cart items have products with sizes but no size selected
  bool get hasItemsWithMissingSizes {
    return cartItems.any((item) => item.isSizeRequired);
  }
}
