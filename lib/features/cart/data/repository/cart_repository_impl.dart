import 'package:dio/dio.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/exceptions/exceptions.dart';
import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/cart/data/datasource/cart_datasource.dart';
import 'package:echipta/features/cart/domain/entities/basket_price_entity.dart';
import 'package:echipta/features/cart/domain/entities/cart_item_entity.dart';
import 'package:echipta/features/cart/domain/entities/order_entity.dart';
import 'package:echipta/features/cart/domain/entities/order_status_entity.dart';
import 'package:echipta/features/cart/domain/entities/my_orders_response_entity.dart';
import 'package:echipta/features/cart/domain/entities/my_order_entity.dart';
import 'package:echipta/features/cart/domain/entities/my_order_item_entity.dart';
import 'package:echipta/features/cart/domain/repository/cart_repository.dart';

class CartRepositoryImpl implements CartRepository {
  final CartDatasource _datasource = serviceLocator<CartDatasourceImpl>();

  @override
  Future<Either<Failure, BasketPriceEntity>> getBasketPrice(
    List<CartItemEntity> items,
    DeliveryType deliveryType,
  ) async {
    try {
      final result = await _datasource.getBasketPrice(items, deliveryType);
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(
        ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey,
        ),
      );
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }

  @override
  Future<Either<Failure, OrderEntity>> createOrder(
    List<CartItemEntity> items,
    DeliveryType deliveryType,
    String? deliveryAddress,
    PaymentType paymentType,
  ) async {
    try {
      final result = await _datasource.createOrder(items, deliveryType, deliveryAddress, paymentType);
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(
        ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey,
        ),
      );
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }

  @override
  Future<Either<Failure, OrderStatusEntity>> getOrderStatus(int orderId) async {
    try {
      final result = await _datasource.getOrderStatus(orderId);
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(
        ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey,
        ),
      );
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }

  @override
  Future<Either<Failure, MyOrdersResponseEntity>> getMyOrders({int page = 1}) async {
    try {
      final result = await _datasource.getMyOrders(page: page);
      // Convert model to entity
      final entity = MyOrdersResponseEntity(
        items: result.items.map((item) => MyOrderEntity(
          id: item.id,
          type: item.type,
          status: item.status,
          paymentUrl: item.paymentUrl,
          isGifted: item.isGifted,
          amount: item.amount,
          ticket: item.ticket,
          items: item.items.map((orderItem) => MyOrderItemEntity(
            itemId: orderItem.itemId,
            product: orderItem.product,
          )).toList(),
        )).toList(),
        meta: MyOrdersMetaEntity(
          currentPage: result.meta.currentPage,
          lastPage: result.meta.lastPage,
          perPage: result.meta.perPage,
          total: result.meta.total,
        ),
        links: MyOrdersLinksEntity(
          first: result.links.first,
          last: result.links.last,
          prev: result.links.prev,
          next: result.links.next,
        ),
      );
      return Right(entity);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(
        ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey,
        ),
      );
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }
}
