import 'package:dio/dio.dart';
import 'package:echipta/core/api/dio_settings.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/exceptions/exceptions.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/features/cart/data/models/basket_price_model.dart';
import 'package:echipta/features/cart/data/models/order_model.dart';
import 'package:echipta/features/cart/data/models/order_status_model.dart';
import 'package:echipta/features/cart/data/models/my_orders_response_model.dart';
import 'package:echipta/features/cart/domain/entities/cart_item_entity.dart';
import 'package:echipta/features/cart/domain/entities/order_entity.dart';

abstract class CartDatasource {
  Future<BasketPriceModel> getBasketPrice(List<CartItemEntity> items, DeliveryType deliveryType);
  Future<OrderModel> createOrder(List<CartItemEntity> items, DeliveryType deliveryType, String? deliveryAddress, PaymentType paymentType);
  Future<OrderStatusModel> getOrderStatus(int orderId);
  Future<MyOrdersResponseModel> getMyOrders({int page = 1});
}

class CartDatasourceImpl implements CartDatasource {
  final Dio _dio = serviceLocator<DioSettings>().dio;

  @override
  Future<BasketPriceModel> getBasketPrice(List<CartItemEntity> items, DeliveryType deliveryType) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final data = {
        "items": items.map((item) => item.toApiJson()).toList(),
        "delivery_type": deliveryType.apiValue,
      };
      
      final response = await _dio.post(
        "/products/get-basket-price",
        data: data,
        options: Options(headers: {"Authorization": token}),
      );
      
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return BasketPriceModel.fromJson(response.data["data"] as Map<String, dynamic>);
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<OrderModel> createOrder(List<CartItemEntity> items, DeliveryType deliveryType, String? deliveryAddress, PaymentType paymentType) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final data = {
        "items": items.map((item) => item.toApiJson()).toList(),
        "delivery_type": deliveryType.apiValue,
        "payment_type": paymentType.apiValue,
      };

      // Add delivery address if delivery type is delivery
      if (deliveryType == DeliveryType.delivery && deliveryAddress != null) {
        data["delivery_address"] = deliveryAddress;
      }

      final response = await _dio.post(
        "/products/create-order",
        data: data,
        options: Options(headers: {"Authorization": token}),
      );

      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        // Check if response contains error information (like order API pattern)
        if (response.data is Map<String, dynamic>) {
          final responseData = response.data as Map<String, dynamic>;

          // Check for error messages in successful HTTP responses
          if (responseData["message"] == "Balance not enough") {
            throw CustomException(message: "Balance not enough", code: '424');
          }

          // Check for other error patterns
          if (responseData.containsKey("code") && responseData["code"] == 424) {
            final errorMessage = responseData["message"] ?? "Balance not enough";
            throw CustomException(message: errorMessage, code: '424');
          }
        }

        // Handle different data types in response
        final data = response.data["data"];
        if (data is Map<String, dynamic>) {
          return OrderModel.fromJson(data);
        } else if (data is List) {
          // When payment is successful with balance, API returns empty array
          // Return a default OrderModel indicating successful payment
          return const OrderModel(orderId: 0, paymentUrl: null);
        } else {
          throw ParsingException(errorMessage: "Unexpected data format in response");
        }
      } else {
        // Handle specific error messages like "Balance not enough"
        if (response.data is Map<String, dynamic> && response.data["message"] != null) {
          final errorMessage = response.data["message"] as String;
          throw CustomException(message: errorMessage, code: '${response.statusCode}');
        } else {
          final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
          throw CustomException(message: message, code: '${response.statusCode}');
        }
      }
    } on DioException catch (error) {
      // Handle DioException which includes HTTP error responses
      if (error.response != null && error.response!.data is Map<String, dynamic>) {
        final responseData = error.response!.data as Map<String, dynamic>;
        if (responseData["message"] != null) {
          final errorMessage = responseData["message"] as String;
          throw CustomException(message: errorMessage, code: '${error.response!.statusCode}');
        }
      }
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<OrderStatusModel> getOrderStatus(int orderId) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      
      final response = await _dio.get(
        "/games/order-status",
        queryParameters: {"order_id": orderId},
        options: Options(headers: {"Authorization": token}),
      );
      
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return OrderStatusModel.fromJson(response.data["data"] as Map<String, dynamic>);
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<MyOrdersResponseModel> getMyOrders({int page = 1}) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);

      final response = await _dio.get(
        "/products/my-orders",
        queryParameters: {"page": page},
        options: Options(headers: {"Authorization": token}),
      );

      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return MyOrdersResponseModel.fromJson(response.data["data"] as Map<String, dynamic>);
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '142');
    }
  }
}
