// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/cart/data/models/my_order_item_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'my_order_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class MyOrderModel {
  final int id;
  final int type;
  final int status;
  @Json<PERSON>ey(name: 'payment_url')
  final String? paymentUrl;
  @JsonKey(name: 'is_gifted')
  final bool isGifted;
  final int amount;
  final dynamic ticket;
  final List<MyOrderItemModel> items;

  const MyOrderModel({
    this.id = 0,
    this.type = 0,
    this.status = 0,
    this.paymentUrl,
    this.isGifted = false,
    this.amount = 0,
    this.ticket,
    this.items = const [],
  });

  factory MyOrderModel.fromJson(Map<String, dynamic> json) =>
      _$MyOrderModelFromJson(json);

  Map<String, dynamic> toJson() => _$MyOrderModelToJson(this);
}
