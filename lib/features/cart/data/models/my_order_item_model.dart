// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/home/<USER>/models/product_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'my_order_item_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class MyOrderItemModel {
  @JsonKey(name: 'item_id')
  final int itemId;
  final ProductModel product;

  const MyOrderItemModel({
    this.itemId = 0,
    this.product = const ProductModel(),
  });

  factory MyOrderItemModel.fromJson(Map<String, dynamic> json) =>
      _$MyOrderItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$MyOrderItemModelToJson(this);
}
