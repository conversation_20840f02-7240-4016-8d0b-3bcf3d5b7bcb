// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'my_orders_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MyOrdersResponseModel _$MyOrdersResponseModelFromJson(
  Map<String, dynamic> json,
) => MyOrdersResponseModel(
  items:
      (json['items'] as List<dynamic>?)
          ?.map((e) => MyOrderModel.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  meta:
      json['meta'] == null
          ? const MyOrdersMetaModel()
          : MyOrdersMetaModel.fromJson(json['meta'] as Map<String, dynamic>),
  links:
      json['links'] == null
          ? const MyOrdersLinksModel()
          : MyOrdersLinksModel.fromJson(json['links'] as Map<String, dynamic>),
);

Map<String, dynamic> _$MyOrdersResponseModelToJson(
  MyOrdersResponseModel instance,
) => <String, dynamic>{
  'items': instance.items,
  'meta': instance.meta,
  'links': instance.links,
};

MyOrdersMetaModel _$MyOrdersMetaModelFromJson(Map<String, dynamic> json) =>
    MyOrdersMetaModel(
      currentPage: (json['current_page'] as num?)?.toInt() ?? 1,
      lastPage: (json['last_page'] as num?)?.toInt() ?? 1,
      perPage: (json['per_page'] as num?)?.toInt() ?? 10,
      total: (json['total'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$MyOrdersMetaModelToJson(MyOrdersMetaModel instance) =>
    <String, dynamic>{
      'current_page': instance.currentPage,
      'last_page': instance.lastPage,
      'per_page': instance.perPage,
      'total': instance.total,
    };

MyOrdersLinksModel _$MyOrdersLinksModelFromJson(Map<String, dynamic> json) =>
    MyOrdersLinksModel(
      first: json['first'] as String?,
      last: json['last'] as String?,
      prev: json['prev'] as String?,
      next: json['next'] as String?,
    );

Map<String, dynamic> _$MyOrdersLinksModelToJson(MyOrdersLinksModel instance) =>
    <String, dynamic>{
      'first': instance.first,
      'last': instance.last,
      'prev': instance.prev,
      'next': instance.next,
    };
