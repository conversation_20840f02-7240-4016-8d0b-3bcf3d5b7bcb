// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/cart/domain/entities/order_status_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order_status_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class OrderStatusModel extends OrderStatusEntity {
  const OrderStatusModel({
    super.status,
    super.info,
  });

  factory OrderStatusModel.fromJson(Map<String, dynamic> json) =>
      _$OrderStatusModelFromJson(json);

  Map<String, dynamic> toJson() => _$OrderStatusModelToJson(this);
}
