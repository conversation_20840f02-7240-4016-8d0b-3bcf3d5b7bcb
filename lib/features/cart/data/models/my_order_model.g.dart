// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'my_order_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MyOrderModel _$MyOrderModelFromJson(Map<String, dynamic> json) => MyOrderModel(
  id: (json['id'] as num?)?.toInt() ?? 0,
  type: (json['type'] as num?)?.toInt() ?? 0,
  status: (json['status'] as num?)?.toInt() ?? 0,
  paymentUrl: json['payment_url'] as String?,
  isGifted: json['is_gifted'] as bool? ?? false,
  amount: (json['amount'] as num?)?.toInt() ?? 0,
  ticket: json['ticket'],
  items:
      (json['items'] as List<dynamic>?)
          ?.map((e) => MyOrderItemModel.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
);

Map<String, dynamic> _$MyOrderModelToJson(MyOrderModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'status': instance.status,
      'payment_url': instance.paymentUrl,
      'is_gifted': instance.isGifted,
      'amount': instance.amount,
      'ticket': instance.ticket,
      'items': instance.items,
    };
