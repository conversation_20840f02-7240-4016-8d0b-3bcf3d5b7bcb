// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/cart/domain/entities/basket_price_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'basket_price_model.g.dart';

@JsonSerializable()
class BasketPriceModel extends BasketPriceEntity {
  const BasketPriceModel({
    @JsonKey(name: 'totalPrice') super.totalPrice,
    @JsonKey(name: 'deliveryPrice') super.deliveryPrice,
  });

  factory BasketPriceModel.fromJson(Map<String, dynamic> json) =>
      _$BasketPriceModelFromJson(json);

  Map<String, dynamic> toJson() => _$BasketPriceModelToJson(this);
}
