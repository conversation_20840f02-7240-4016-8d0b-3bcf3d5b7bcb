// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/cart/domain/entities/order_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class OrderModel extends OrderEntity {
  const OrderModel({
    super.orderId,
    super.paymentUrl,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) =>
      _$OrderModelFromJson(json);

  Map<String, dynamic> toJson() => _$OrderModelToJson(this);
}
