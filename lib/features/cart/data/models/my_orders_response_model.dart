// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/cart/data/models/my_order_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'my_orders_response_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class MyOrdersResponseModel {
  final List<MyOrderModel> items;
  final MyOrdersMetaModel meta;
  final MyOrdersLinksModel links;

  const MyOrdersResponseModel({
    this.items = const [],
    this.meta = const MyOrdersMetaModel(),
    this.links = const MyOrdersLinksModel(),
  });

  factory MyOrdersResponseModel.fromJson(Map<String, dynamic> json) =>
      _$MyOrdersResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$MyOrdersResponseModelToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake)
class MyOrdersMetaModel {
  @JsonKey(name: 'current_page')
  final int currentPage;
  @JsonKey(name: 'last_page')
  final int lastPage;
  @JsonKey(name: 'per_page')
  final int perPage;
  final int total;

  const MyOrdersMetaModel({
    this.currentPage = 1,
    this.lastPage = 1,
    this.perPage = 10,
    this.total = 0,
  });

  factory MyOrdersMetaModel.fromJson(Map<String, dynamic> json) =>
      _$MyOrdersMetaModelFromJson(json);

  Map<String, dynamic> toJson() => _$MyOrdersMetaModelToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake)
class MyOrdersLinksModel {
  final String? first;
  final String? last;
  final String? prev;
  final String? next;

  const MyOrdersLinksModel({
    this.first,
    this.last,
    this.prev,
    this.next,
  });

  factory MyOrdersLinksModel.fromJson(Map<String, dynamic> json) =>
      _$MyOrdersLinksModelFromJson(json);

  Map<String, dynamic> toJson() => _$MyOrdersLinksModelToJson(this);
}
