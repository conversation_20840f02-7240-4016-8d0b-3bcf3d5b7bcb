// ignore_for_file: deprecated_member_use

import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/features/profile/presentation/widgets/w_profile_item.dart';
import 'package:echipta/features/profile/presentation/widgets/w_profile_user_data.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(LocaleKeys.account.tr(), style: context.textTheme.displaySmall)),
      body: RefreshIndicator.adaptive(
        color: AppColors.primary,
        onRefresh: () async {
          context.read<ProfileBloc>().add(GetMeEvent());
        },
        child: ListView(
          physics: AlwaysScrollableScrollPhysics(parent: ClampingScrollPhysics()),
          padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
          children: [
            WProfileUserData(),
            Gap(20),
            WProfileItem(icon: AppAssets.card, title: "Kartalarim", route: AppRouter.cards, onTap: context.soonFeature),
            Gap(10),
            WProfileItem(icon: AppAssets.prize, title: LocaleKeys.gift.tr(), route: AppRouter.present),
            Gap(10),
            WProfileItem(icon: AppAssets.history, title: LocaleKeys.orderhistory.tr(), route: AppRouter.orderHistory),
            Gap(10),
            WProfileItem(icon: AppAssets.idcard, title: LocaleKeys.myid.tr(), route: AppRouter.idcard),
            Gap(10),
            WProfileItem(icon: AppAssets.ticketsolo, title: LocaleKeys.mytickets.tr(), route: AppRouter.tickets),
            Gap(10),
            WProfileItem(icon: AppAssets.settings, title: LocaleKeys.settings.tr(), route: AppRouter.settings),
            Gap(10),
            WProfileItem(icon: AppAssets.privacy, title: LocaleKeys.privacyPolicy.tr(), route: AppRouter.privacy),
            Gap(10),
            WProfileItem(
              icon: AppAssets.logout,
              title: LocaleKeys.logout.tr(),
              txtColor: AppColors.red,
              iconColor: AppColors.red,
              hasTrailing: true,
              route: "logout",
            ),
          ],
        ),
      ),
    );
  }
}
