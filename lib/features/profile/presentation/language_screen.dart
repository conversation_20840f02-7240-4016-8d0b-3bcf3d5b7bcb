import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/services/notification_service.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';

class LanguageScreen extends StatelessWidget {
  const LanguageScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          LocaleKeys.appLang.tr(),
          style: context.textTheme.displaySmall,
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            GestureDetector(
              onTap: () async {
                context.setLocale(const Locale("uz"));
                await StorageRepository.putString(StoreKeys.language, "uz");
                // Update FCM topic subscription to match new language
                NotificationService().ensureLanguageTopicSubscription();
              },
              child: Container(
                height: 50,
                decoration: BoxDecoration(
                  border:
                      context.locale.languageCode == "uz"
                          ? Border.all(color: AppColors.primary)
                          : null,
                  color: AppColors.mediumGrey,
                  borderRadius: BorderRadius.circular(200),
                ),
                alignment: Alignment.center,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(AppAssets.uz),
                    const Gap(10),
                    Text("O‘zbek tili", style: context.textTheme.labelLarge),
                  ],
                ),
              ),
            ),
            const Gap(16),
            GestureDetector(
              onTap: () async {
                context.setLocale(const Locale("ru"));
                await StorageRepository.putString(StoreKeys.language, "ru");
                // Update FCM topic subscription to match new language
                NotificationService().ensureLanguageTopicSubscription();
              },
              child: Container(
                height: 50,
                decoration: BoxDecoration(
                  border:
                      context.locale.languageCode == "ru"
                          ? Border.all(color: AppColors.primary)
                          : null,
                  color: AppColors.mediumGrey,
                  borderRadius: BorderRadius.circular(200),
                ),
                alignment: Alignment.center,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(AppAssets.ru),
                    const Gap(10),
                    Text("Русский язык", style: context.textTheme.labelLarge),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
