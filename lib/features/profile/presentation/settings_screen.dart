import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/services/notification_service.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/profile/presentation/widgets/w_profile_item.dart';
import 'package:echipta/features/profile/presentation/widgets/w_profile_user_data.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        foregroundColor: AppColors.black,
        title: Text(
          LocaleKeys.settings.tr(),
          style: context.textTheme.displaySmall!.copyWith(
            color: AppColors.black,
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            WProfileItem(
              title: LocaleKeys.appPin.tr(),
              icon: AppAssets.lock,
              route: AppRouter.setPincode,
            ),
            Gap(16),
            WProfileItem(
              title: LocaleKeys.appLang.tr(),
              icon: AppAssets.language,
              route: AppRouter.lang,
            ),
            // Debug notification button (only in debug mode)
            if (kDebugMode) ...[
              Gap(16),
              GestureDetector(
                onTap: () async {
                  print("🧪 Testing notification...");
                  await NotificationService().showTestNotification();
                },
                child: Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.primary3,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.primary, width: 1),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.notifications_active, color: AppColors.primary),
                      Gap(12),
                      Expanded(
                        child: Text(
                          "Test Notification",
                          style: TextStyle(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Icon(Icons.arrow_forward_ios, color: AppColors.primary, size: 16),
                    ],
                  ),
                ),
              ),
              Gap(16),
              GestureDetector(
                onTap: () async {
                  print("🧪 Testing notification with image...");
                  await NotificationService().showTestNotificationWithImage();
                },
                child: Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.yellow3,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.yellow, width: 1),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.notifications_active, color: AppColors.yellow),
                      Gap(12),
                      Expanded(
                        child: Text(
                          "Test Notification with Image",
                          style: TextStyle(
                            color: AppColors.yellow,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Icon(Icons.arrow_forward_ios, color: AppColors.yellow, size: 16),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
