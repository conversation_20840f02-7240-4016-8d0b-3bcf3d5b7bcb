import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/common/widgets/w_text_field.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';

class ProfileEditScreen extends StatefulWidget {
  const ProfileEditScreen({super.key});

  @override
  State<ProfileEditScreen> createState() => _ProfileEditScreenState();
}

class _ProfileEditScreenState extends State<ProfileEditScreen> {
  final TextEditingController fullNameController = TextEditingController();
  final TextEditingController birthDateController = TextEditingController();
  XFile? imageFile;

  @override
  void initState() {
    super.initState();
    final user = context.read<ProfileBloc>().state.me;
    fullNameController.text = user.full_name;
    birthDateController.text = user.birth_date;
    // imageFile = File.fromUri(Uri.parse(user.picture));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Profilni tahrirlash",
          style: context.textTheme.displaySmall,
        ),
      ),
      body: BlocBuilder<ProfileBloc, ProfileState>(
        builder: (context, state) {
          final user = state.me;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: Column(
              children: [
                Center(
                  child: SizedBox(
                    width: 100,
                    height: 100,
                    child: Stack(
                      alignment: Alignment.bottomRight,
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(200),
                          child: _buildProfileImage(user),
                        ),
                        GestureDetector(
                          // onTap: () => _showImagePickerOptions(),
                          onTap: () => _pickImage(ImageSource.camera),
                          child: Container(
                            width: 30,
                            height: 30,
                            alignment: Alignment.center,
                            decoration: const BoxDecoration(
                              color: AppColors.yellow,
                              shape: BoxShape.circle,
                            ),
                            child: SvgPicture.asset(AppAssets.edit),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Gap(20),
                WTextField(
                  onChanged: (value) {
                    context.read<AuthBloc>().add(EnterFullNameEvent(value));
                  },
                  controller: fullNameController,
                  isEmpty: fullNameController.text.isEmpty,
                  labelText: LocaleKeys.fio.tr(),
                  prefixIcon: Icons.person,
                ),
                const Gap(16),
                WTextField(
                  onChanged: (value) {
                    context.read<AuthBloc>().add(EnterBirthDateEvent(value));
                  },
                  controller: birthDateController,
                  isEmpty: birthDateController.text.isEmpty,
                  labelText: LocaleKeys.birthDate.tr(),
                  prefixIcon: Icons.date_range_outlined,
                ),
                Spacer(),
                BlocListener<AuthBloc, AuthState>(
                  listener: (context, authState) {
                    if (authState.registerStatus == FormzSubmissionStatus.success) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text("Profil muvaffaqiyatli yangilandi"),
                          backgroundColor: AppColors.primary,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      );
                      // Refresh profile data
                      context.read<ProfileBloc>().add(GetMeEvent());
                      Navigator.pop(context);
                    } else if (authState.registerStatus == FormzSubmissionStatus.failure) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text("Xatolik yuz berdi"),
                          backgroundColor: AppColors.red,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      );
                    }
                  },
                  child: BlocBuilder<AuthBloc, AuthState>(
                    builder: (context, authState) {
                      return WButton(
                        onTap: () {
                          _updateProfile();
                        },
                        txt: authState.registerStatus == FormzSubmissionStatus.inProgress
                            ? "Saqlanmoqda..."
                            : "Saqlash",
                        isLoading: authState.registerStatus == FormzSubmissionStatus.inProgress,
                      );
                    },
                  ),
                ),
                Gap(10),
                WButton(
                  onTap: () {
                    context.read<AuthBloc>().add(
                      DeleteUserEvent(
                        onError: (p0) {},
                        onSuccess: () {
                          context.go(AppRouter.auth);
                          context.read<AuthBloc>().add(LogoutEvent());
                        },
                      ),
                    );
                  },
                  txt: "Profilni o'chirish",
                  btnColor: AppColors.red,
                  txtColor: AppColors.white,
                ),
                Gap(context.padding.bottom),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfileImage(user) {
    if (imageFile != null) {
      // Show selected local image
      return Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          image: DecorationImage(
            image: FileImage(File(imageFile!.path)),
            fit: BoxFit.cover,
          ),
          border: Border.all(
            color: AppColors.primary,
            width: 3,
          ),
        ),
      );
    } else {
      // Show network image or placeholder
      return CachedNetworkImage(
        imageUrl: user.picture,
        width: 100,
        fit: BoxFit.cover,
        height: 100,
        placeholder: (context, url) => Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: AppColors.fillColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: CircularProgressIndicator(
              color: AppColors.primary,
              strokeWidth: 2,
            ),
          ),
        ),
        errorWidget: (context, url, error) {
          return Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: AppColors.darkGrey,
              borderRadius: BorderRadius.circular(100),
            ),
            child: SvgPicture.asset(
              AppAssets.person,
              width: 50,
              color: AppColors.white,
            ),
          );
        },
      );
    }
  }

  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.mediumGrey,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Gap(20),
                Text(
                  "Rasm tanlash",
                  style: context.textTheme.titleLarge!.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Gap(20),
                Row(
                  children: [
                    Expanded(
                      child: _buildImageOption(
                        icon: Icons.camera_alt,
                        title: "Kamera",
                        onTap: () => _pickImage(ImageSource.camera),
                      ),
                    ),
                    const Gap(16),
                    Expanded(
                      child: _buildImageOption(
                        icon: Icons.photo_library,
                        title: "Galereya",
                        onTap: () => _pickImage(ImageSource.gallery),
                      ),
                    ),
                  ],
                ),
                if (imageFile != null) ...[
                  const Gap(16),
                  _buildImageOption(
                    icon: Icons.delete,
                    title: "Rasmni o'chirish",
                    onTap: () {
                      setState(() {
                        imageFile = null;
                      });
                      Navigator.pop(context);
                    },
                    color: AppColors.red,
                  ),
                ],
                const Gap(20),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildImageOption({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: (color ?? AppColors.primary).withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: (color ?? AppColors.primary).withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: (color ?? AppColors.primary).withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: color ?? AppColors.primary,
                size: 24,
              ),
            ),
            const Gap(8),
            Text(
              title,
              style: context.textTheme.bodyMedium!.copyWith(
                color: color ?? AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    // Navigator.pop(context); // Close bottom sheet

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        imageQuality: 70,
        maxWidth: 800,
        maxHeight: 800,
        preferredCameraDevice: CameraDevice.front,
      );

      if (image != null) {
        setState(() {
          imageFile = image;
        });

        // Show success feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Rasm tanlandi"),
            backgroundColor: AppColors.primary,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } catch (e) {
      // Show error feedback
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Rasm tanlashda xatolik yuz berdi"),
          backgroundColor: AppColors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
  }

  void _updateProfile() {
    // Update AuthBloc state with current form values
    context.read<AuthBloc>().add(EnterFullNameEvent(fullNameController.text));
    context.read<AuthBloc>().add(EnterBirthDateEvent(birthDateController.text));

    // Submit the update
    context.read<AuthBloc>().add(
      SubmitRegisterEvent(
        file: imageFile ?? XFile(""),
        onError: (error) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text("Xatolik: $error"),
              backgroundColor: AppColors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        },
        onSuccess: () {
          // Success is handled by BlocListener
        },
      ),
    );
  }
}
