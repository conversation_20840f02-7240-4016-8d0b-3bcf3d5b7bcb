import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/core/utils/size_config.dart';
import 'package:echipta/features/order/domain/entities/order_history_entity.dart';
import 'package:echipta/features/order/presentation/widgets/w_bill_information_item.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';

class WTicketOrderItem extends StatelessWidget {
  const WTicketOrderItem({super.key, required this.item});

  final OrderHistoryEntity item;

  @override
  Widget build(BuildContext context) {
    var formattedDate = DateFormat(
      'dd MMMM yyyy HH:mm',
    ).format(item.ticket?.match.start_date?.toLocal() ?? DateTime.now());
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.mediumGrey),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CachedNetworkImage(
                imageUrl: item.ticket?.match.main_team.image ?? "",
                width: wi(60),
                height: he(60),
                errorWidget: (context, url, error) {
                  return const Placeholder();
                },
              ),
              const Gap(20),
              Image.asset(AppAssets.vs, width: wi(80)),
              const Gap(20),
              CachedNetworkImage(
                imageUrl: item.ticket?.match.second_team.image ?? "",
                width: wi(60),
                height: he(60),
                errorWidget: (context, url, error) {
                  return const Placeholder();
                },
              ),
            ],
          ),
          const Gap(20),
          Column(
            children: [
              WBillInformationItem(
                title: "Qo'shimcha ma'lumot:",
                subtitle:
                    "${item.ticket?.sector} | ${item.ticket?.row} | ${item.ticket?.seat} joy",
              ),
              const Gap(5),
              WBillInformationItem(
                title: "Boshlanish vaqti:",
                subtitle: formattedDate,
              ),
              const Gap(10),
              WBillInformationItem(
                title: "Narxi:",
                subtitle:
                    "${item.amount.toDouble().formatAsSpaceSeparated()} so'm",
              ),
            ],
          ),
        ],
      ),
    );
  }
}
