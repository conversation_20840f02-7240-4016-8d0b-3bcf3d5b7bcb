import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/features/profile/domain/entities/id_card_entity.dart';
import 'package:flip_card/flip_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/profile_bloc.dart';

class WFlipCard extends StatefulWidget {
  const WFlipCard({super.key, required this.front, required this.back, this.idCard});

  final String front;
  final String back;
  final IdCardEntity? idCard;

  @override
  State<WFlipCard> createState() => _WFlipCardState();
}

class _WFlipCardState extends State<WFlipCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _shimmerController;
  bool _frontImageLoaded = false;
  bool _backImageLoaded = false;
  bool _frontImageError = false;
  bool _backImageError = false;

  @override
  void initState() {
    super.initState();
    _shimmerController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    _startShimmerAnimation();
  }

  void _startShimmerAnimation() {
    if (!_frontImageLoaded || !_backImageLoaded) {
      _shimmerController.repeat();
    }
  }

  void _stopShimmerAnimation() {
    _shimmerController.stop();
  }

  void _onImageLoaded(bool isFront) {
    setState(() {
      if (isFront) {
        _frontImageLoaded = true;
        _frontImageError = false; // Reset error state on successful load
      } else {
        _backImageLoaded = true;
        _backImageError = false; // Reset error state on successful load
      }
    });

    // Stop shimmer when both images are loaded
    if (_frontImageLoaded && _backImageLoaded) {
      _stopShimmerAnimation();
    }
  }

  void _onImageError(bool isFront) {
    setState(() {
      if (isFront) {
        _frontImageError = true;
        _frontImageLoaded = false;
      } else {
        _backImageError = true;
        _backImageLoaded = false;
      }
    });

    // Stop shimmer on error
    _stopShimmerAnimation();
  }

  // Check if flip should be disabled due to errors or invalid URLs
  bool get _shouldDisableFlip {
    return _frontImageError || _backImageError || _cardNotReady;
  }

  // Check if card is not ready (status is not active)
  bool get _cardNotReady {
    final status = widget.idCard?.status;
    return status == null || status != "active";
  }

  // Check if URLs are invalid (for error messaging)
  bool get _hasInvalidUrls {
    return _isInvalidUrl(widget.front) || _isInvalidUrl(widget.back);
  }

  bool _isInvalidUrl(String url) {
    // Check for common invalid URL patterns
    if (url.isEmpty) return true;
    if (url == "https://dev.echipta.uz/driver") return true;
    if (url.endsWith("/driver")) return true;
    if (!url.contains('.')) return true; // No file extension

    // Check if URL has a valid image extension
    final validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
    final hasValidExtension = validExtensions.any((ext) =>
        url.toLowerCase().contains(ext));

    return !hasValidExtension;
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // If there are errors, show a non-flippable container
    if (_shouldDisableFlip) {
      return _buildErrorPlaceholder();
    }

    return FlipCard(
      direction: FlipDirection.VERTICAL,
      speed: 600,
      onFlipDone: (status) {
        // Optional: Handle flip completion
      },
      front: _buildCardSide(
        imageUrl: widget.front,
        isFront: true,
      ),
      back: _buildCardSide(
        imageUrl: widget.back,
        isFront: false,
      ),
    );
  }

  Widget _buildCardSide({
    required String imageUrl,
    required bool isFront,
  }) {
    final isLoaded = isFront ? _frontImageLoaded : _backImageLoaded;

    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: Container(
        width: double.maxFinite,
        height: MediaQuery.of(context).size.height * 0.25,
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: AppColors.darkGrey.withOpacity(0.1),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: CachedNetworkImage(
          imageUrl: imageUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => _buildPlaceholder(),
          errorWidget: (context, url, error) {
            print('Image loading error for $imageUrl: $error');
            // Notify that image failed to load
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _onImageError(isFront);
            });
            return _buildErrorPlaceholder();
          },
          imageBuilder: (context, imageProvider) {
            // Notify that image is loaded
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _onImageLoaded(isFront);
            });

            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                image: DecorationImage(
                  image: imageProvider,
                  fit: BoxFit.cover,
                ),
              ),
            );
          },
          httpHeaders: const {
            'User-Agent': 'Mozilla/5.0 (compatible; Flutter app)',
          },
          maxWidthDiskCache: 1000,
          maxHeightDiskCache: 600,
          memCacheWidth: 1000,
          memCacheHeight: 600,
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.25,
      width: double.maxFinite,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withOpacity(0.1),
            AppColors.primary.withOpacity(0.05),
            AppColors.fillColor,
          ],
          stops: const [0.0, 0.3, 1.0],
        ),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.1),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Animated shimmer effect
          AnimatedBuilder(
            animation: _shimmerController,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  gradient: LinearGradient(
                    begin: Alignment(-1.0 + (_shimmerController.value * 3), -0.5),
                    end: Alignment(1.0 + (_shimmerController.value * 3), 0.5),
                    colors: [
                      Colors.transparent,
                      AppColors.white.withOpacity(0.4),
                      AppColors.white.withOpacity(0.7),
                      AppColors.white.withOpacity(0.4),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
                  ),
                ),
              );
            },
          ),
          // Loading indicator
          Positioned(
            top: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.white.withOpacity(0.9),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppColors.primary.withOpacity(0.7),
                  ),
                ),
              ),
            ),
          ),
          // Card content placeholder
          Positioned.fill(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top section - Logo area
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [

                      AnimatedBuilder(
                        animation: _shimmerController,
                        builder: (context, child) {
                          return Container(
                            width: 90,
                            height: 35,
                            decoration: BoxDecoration(
                              color: AppColors.primary.withOpacity(
                                0.1 + (_shimmerController.value * 0.1),
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Center(
                              child: Text(
                                'ID CARD',
                                style: TextStyle(
                                  color: AppColors.primary.withOpacity(0.3),
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 1.5,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  // Chip placeholder
                  AnimatedBuilder(
                    animation: _shimmerController,
                    builder: (context, child) {
                      return Container(
                        width: 50,
                        height: 35,
                        decoration: BoxDecoration(
                          color: AppColors.primary.withOpacity(
                            0.1 + (_shimmerController.value * 0.1),
                          ),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: AppColors.primary.withOpacity(0.15),
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          Icons.sports_soccer_outlined,
                          color: AppColors.primary.withOpacity(0.3),
                          size: 16,
                        ),
                      );
                    },
                  ),
                  const Spacer(),
                  // Card number placeholder with dots
                  AnimatedBuilder(
                    animation: _shimmerController,
                    builder: (context, child) {
                      return Row(
                        children: [
                          for (int i = 0; i < 4; i++) ...[
                            Container(
                              width: 45,
                              height: 18,
                              decoration: BoxDecoration(
                                color: AppColors.primary.withOpacity(
                                  0.15 + (_shimmerController.value * 0.1),
                                ),
                                borderRadius: BorderRadius.circular(9),
                              ),
                            ),
                            if (i < 3) const SizedBox(width: 12),
                          ],
                        ],
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  // Bottom section - Name and expiry
                  Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: AnimatedBuilder(
                          animation: _shimmerController,
                          builder: (context, child) {
                            return Container(
                              height: 14,
                              decoration: BoxDecoration(
                                color: AppColors.primary.withOpacity(
                                  0.1 + (_shimmerController.value * 0.1),
                                ),
                                borderRadius: BorderRadius.circular(7),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 24),
                      Expanded(
                        child: AnimatedBuilder(
                          animation: _shimmerController,
                          builder: (context, child) {
                            return Container(
                              height: 14,
                              decoration: BoxDecoration(
                                color: AppColors.primary.withOpacity(
                                  0.1 + (_shimmerController.value * 0.1),
                                ),
                                borderRadius: BorderRadius.circular(7),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorPlaceholder() {
    return AnimatedContainer(
      width: double.maxFinite,
      height: MediaQuery.of(context).size.height * 0.25,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.fillColor, AppColors.fillColor.withOpacity(0.8)],
        ),
        border: Border.all(
          color: AppColors.mediumGrey.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.darkGrey.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _cardNotReady
                    ? AppColors.primary.withOpacity(0.1)
                    : AppColors.red.withOpacity(0.1),
                border: Border.all(
                  color: _cardNotReady
                      ? AppColors.primary.withOpacity(0.2)
                      : AppColors.red.withOpacity(0.2),
                  width: 2,
                ),
              ),
              child: Icon(
                _cardNotReady
                    ? Icons.schedule_outlined
                    : Icons.credit_card_off_outlined,
                size: 32,
                color: _cardNotReady
                    ? AppColors.primary.withOpacity(0.8)
                    : AppColors.red.withOpacity(0.8),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _cardNotReady
                  ? 'ID karta tayyorlanmoqda'
                  : 'Karta yuklanmadi',
              style: TextStyle(
                color: AppColors.darkGrey,
                fontSize: 16,
                fontWeight: FontWeight.w600,
                letterSpacing: 0.2,
              ),
            ),
            const SizedBox(height: 6),
            Text(
              _cardNotReady
                  ? 'ID kartangiz hozircha tayyor emas. Biroz kuting.'
                  : 'Qaytadan urinib ko\'ring',
              style: TextStyle(
                color: AppColors.lightGrey,
                fontSize: 13,
                fontWeight: FontWeight.w400,
                letterSpacing: 0.1,
              ),
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () {
                context.read<ProfileBloc>().add(GetIdCardEvent());
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: AppColors.primary.withOpacity(0.1),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.refresh_rounded,
                      size: 16,
                      color: AppColors.primary,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Yangilash',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
