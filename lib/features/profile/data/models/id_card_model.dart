// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/profile/domain/entities/id_card_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'id_card_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class IdCardModel extends IdCardEntity {
  const IdCardModel({
    super.serial_number,
    super.front_image,
    super.back_image,
    super.status,
  });

  factory IdCardModel.fromJson(Map<String, dynamic> json) =>
      _$IdCardModelFromJson(json);
}
