import 'dart:io';
import 'dart:ui';

import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/features/navigation/domain/entities/navbar.dart';
import 'package:echipta/features/navigation/presentation/navigator.dart';
import 'package:echipta/features/navigation/presentation/widgets/nav_bar_item.dart';
import 'package:echipta/features/cart/presentation/bloc/cart_bloc.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:no_screenshot/no_screenshot.dart';
import 'package:vibration/vibration.dart';
import '../../../generated/locale_keys.g.dart';

enum NavItemEnum { main, rating, cart, ticket, chat, profile }



class NavigationScreen extends StatefulWidget {
  final int? initialTab;
  final bool forceTabChange;

  const NavigationScreen({super.key, this.initialTab, this.forceTabChange = false});

  static Route route() => MaterialPageRoute<void>(builder: (_) => const NavigationScreen());

  @override
  State<NavigationScreen> createState() => _NavigationScreenState();
}

class _NavigationScreenState extends State<NavigationScreen> with TickerProviderStateMixin {
  late PageController _controller;

  final Map<NavItemEnum, GlobalKey<NavigatorState>> _navigatorKeys = {
    NavItemEnum.main: GlobalKey<NavigatorState>(),
    NavItemEnum.rating: GlobalKey<NavigatorState>(),
    NavItemEnum.cart: GlobalKey<NavigatorState>(),
    NavItemEnum.ticket: GlobalKey<NavigatorState>(),
    NavItemEnum.chat: GlobalKey<NavigatorState>(),
    NavItemEnum.profile: GlobalKey<NavigatorState>(),
  };

  final List<NavBar> lables = [
    const NavBar(title: LocaleKeys.main, id: 0, defIcon: AppAssets.home, selectedIcon: AppAssets.home),
    const NavBar(title: LocaleKeys.rating, id: 1, defIcon: AppAssets.rating, selectedIcon: AppAssets.rating),
    const NavBar(title: "Savat", id: 2, defIcon: AppAssets.basket, selectedIcon: AppAssets.basket),
    const NavBar(title: LocaleKeys.currentTicket, id: 3, defIcon: AppAssets.ticket, selectedIcon: AppAssets.ticket),
    const NavBar(title: LocaleKeys.help, id: 4, defIcon: AppAssets.chat, selectedIcon: AppAssets.chat),
    const NavBar(title: LocaleKeys.account, id: 5, defIcon: AppAssets.profile, selectedIcon: AppAssets.profile),
  ];

  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    final initialPage = widget.initialTab ?? 0;
    print('🏠 NavigationScreen initializing with tab: $initialPage (widget.initialTab: ${widget.initialTab})');
    _currentIndex = initialPage;
    _controller = PageController(initialPage: initialPage);
    _controller.addListener(onTabChange);
  }

  // @override
  // void didUpdateWidget(NavigationScreen oldWidget) {
  //   super.didUpdateWidget(oldWidget);
  //   print('🏠 didUpdateWidget called - old: ${oldWidget.initialTab}, new: ${widget.initialTab}, forceTabChange: ${widget.forceTabChange}');
  //   // Only jump to the specified tab when explicitly requested via forceTabChange flag
  //   if (widget.forceTabChange && widget.initialTab != null) {
  //     print('🏠 NavigationScreen updating to tab: ${widget.initialTab}');
  //     final newTab = widget.initialTab!;
  //     _currentIndex = newTab;
  //     _controller.jumpToPage(newTab);
  //   } else {
  //     print('🏠 No tab change - forceTabChange: ${widget.forceTabChange}, initialTab: ${widget.initialTab}');
  //   }
  // }

  void onTabChange() {

    ///Dismiss any keyboard
    FocusManager.instance.primaryFocus?.unfocus();

    final _noScreenshot = NoScreenshot.instance;

    if (kDebugMode) {
      print('Page changed to: ${_controller.page}');
    }

    setState(() {
      _currentIndex = _controller.page?.toInt() ?? 0;
      _navigatorKeys[NavItemEnum.values[_currentIndex]]?.currentState?.popUntil((route) => route.isFirst);
    });

    // Refresh user balance when switching to profile tab (index 5)
    if (_currentIndex == 5) {
      context.read<ProfileBloc>().add(GetMeEvent());
    }

    ///Indexes preserve even we switch visibility
    if (_currentIndex == 3) {
      _noScreenshot.screenshotOff();
    } else {
      _noScreenshot.screenshotOn();
    }
  }

  Widget _buildPageNavigator(NavItemEnum tabItem) =>
      TabNavigator(navigatorKey: _navigatorKeys[tabItem]!, tabItem: tabItem);

  void changePage(int index) {
    setState(() => _currentIndex = index);
    _controller.jumpToPage(index);
  }

  bool isBtmSheetOpened = false;

  @override
  Widget build(BuildContext context) => HomeTabControllerProvider(
    controller: _controller,
    child: Scaffold(
      extendBody: true,
      resizeToAvoidBottomInset: true,
      bottomNavigationBar: Container(
        height: 70 + MediaQuery.of(context).padding.bottom,
        decoration: BoxDecoration(
          color: AppColors.white.withOpacity(0.85),
          borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
          border: const Border.fromBorderSide(BorderSide(width: 1, color: AppColors.white)),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5, tileMode: TileMode.decal),
            child: Row(
              children: [
                NavItemWidget(
                  navBar: lables[0],
                  currentIndex: _currentIndex,
                  onTap: () {
                    vibrateOnTab();
                    changePage(0);
                  },
                ),
                NavItemWidget(
                  navBar: lables[1],
                  currentIndex: _currentIndex,
                  onTap: () {
                    vibrateOnTab();
                    changePage(1);
                  },
                ),
                ///Visible only if cart is not empty, it should work reactively
                BlocBuilder<CartBloc, CartState>(
                  builder: (context, state) {
                    return Visibility(
                      visible: state.isNotEmpty,
                      child: NavItemWidget(
                        navBar: lables[2],
                        currentIndex: _currentIndex,
                        onTap: () {
                          vibrateOnTab();
                          changePage(2);
                        },
                      ),
                    );
                  },
                ),
                NavItemWidget(
                  navBar: lables[3],
                  currentIndex: _currentIndex,
                  onTap: () {
                    vibrateOnTab();
                    changePage(3);
                  },
                ),
                NavItemWidget(
                  navBar: lables[4],
                  currentIndex: _currentIndex,
                  onTap: () {
                    vibrateOnTab();
                    changePage(4);
                  },
                ),
                NavItemWidget(
                  navBar: lables[5],
                  currentIndex: _currentIndex,
                  onTap: () {
                    vibrateOnTab();
                    changePage(5);
                  },
                ),
              ],
            ),
          ),
        ),
      ),
      body: PageView(
        controller: _controller,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          _buildPageNavigator(NavItemEnum.main),
          _buildPageNavigator(NavItemEnum.rating),
          _buildPageNavigator(NavItemEnum.cart),
          _buildPageNavigator(NavItemEnum.ticket),
          _buildPageNavigator(NavItemEnum.chat),
          _buildPageNavigator(NavItemEnum.profile),
        ],
      ),
    ),
  );

  void vibrateOnTab() async {
    if (Platform.isIOS) {
      await HapticFeedback.lightImpact();
    } else if (Platform.isAndroid && (await Vibration.hasVibrator() ?? false)) {
      Vibration.vibrate(amplitude: 50, duration: 40, repeat: 1);
      // HapticFeedback.mediumImpact();
    }
  }
}

class HomeTabControllerProvider extends InheritedWidget {
  final PageController controller;

  const HomeTabControllerProvider({required super.child, required this.controller, super.key});

  static HomeTabControllerProvider of(BuildContext context) {
    final result = context.dependOnInheritedWidgetOfExactType<HomeTabControllerProvider>();
    assert(result != null, 'No HomeTabControllerProvider found in context');
    return result!;
  }

  @override
  bool updateShouldNotify(HomeTabControllerProvider oldWidget) => false;
}
