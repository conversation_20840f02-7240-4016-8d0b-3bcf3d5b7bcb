import 'package:echipta/features/chat/presentation/chat_screen.dart';
import 'package:echipta/features/home/<USER>/home_screen.dart';
import 'package:echipta/features/navigation/presentation/navigation_screen.dart';
import 'package:echipta/features/profile/presentation/profile_screen.dart';
import 'package:echipta/features/ball_rating/presentation/ball_rating_screen.dart';
import 'package:echipta/features/ticket/presentation/current_ticket_screen.dart';
import 'package:echipta/features/cart/presentation/cart_screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

class TabNavigatorRoutes {
  static const String root = '/';
}

class TabNavigator extends StatefulWidget {
  final GlobalKey<NavigatorState> navigatorKey;
  final NavItemEnum tabItem;

  const TabNavigator(
      {required this.tabItem, required this.navigatorKey, super.key});

  @override
  State<TabNavigator> createState() => _TabNavigatorState();
}

class _TabNavigatorState extends State<TabNavigator>
    with AutomaticKeepAliveClientMixin {
  Map<String, WidgetBuilder> _routeBuilders(
      {required BuildContext context, required RouteSettings routeSettings}) {
    switch (widget.tabItem) {
      case NavItemEnum.main:
        return {
          TabNavigatorRoutes.root: (context) => const HomeScreen(),
          // const HomeScreen(),
        };
      case NavItemEnum.rating:
        return {
          TabNavigatorRoutes.root: (context) => const BallRatingScreen(),
          // const BallRatingScreen(),
        };
      case NavItemEnum.cart:
        return {
          TabNavigatorRoutes.root: (context) => const CartScreen(),
        };
      case NavItemEnum.ticket:
        return {
          TabNavigatorRoutes.root: (context) => const CurrentTicketScreen(),
          // const MarketScreen(),
        };
      case NavItemEnum.chat:
        {
          // context.read<FavoritesBloc>().add(GetFavorites());
          return {TabNavigatorRoutes.root: (context) => const ChatScreen()};
        }
      case NavItemEnum.profile:
        return {
          TabNavigatorRoutes.root: (context) => const ProfileScreen(),
        };
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Navigator(
      key: widget.navigatorKey,
      initialRoute: TabNavigatorRoutes.root,
      requestFocus: false,
      onGenerateRoute: (routeSettings) {
        final routeBuilders =
            _routeBuilders(context: context, routeSettings: routeSettings);
        return MaterialWithModalsPageRoute(
          builder: (context) => routeBuilders.containsKey(routeSettings.name)
              ? routeBuilders[routeSettings.name]!(context)
              : Container(),
        );
      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}
