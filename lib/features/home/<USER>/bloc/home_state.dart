// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'home_bloc.dart';
// Updated HomeState class with events support

class HomeState extends Equatable {
  final List<ItemEntity> matchCategories;
  final FormzSubmissionStatus matchCategoriesStatus;
  final List<ItemEntity> selectedMatchCategory;
  final bool showSearch;
  final GameEntity game;
  final FormzSubmissionStatus gameStatus;
  final List<ProductEntity> products;
  final FormzSubmissionStatus productsStatus;
  final List<TicketEntity> myTickets;
  final FormzSubmissionStatus myTicketsStatus;
  final List<NewsEntity> news;
  final FormzSubmissionStatus newsStatus;
  final List<NewsEntity> events; // New field for events
  final FormzSubmissionStatus eventsStatus; // New field for events status
  final AppUpdateStatus appUpdateStatus;

  const HomeState({
    this.matchCategories = const [],
    this.matchCategoriesStatus = FormzSubmissionStatus.initial,
    this.selectedMatchCategory = const [],
    this.showSearch = false,
    this.game = const GameEntity(),
    this.gameStatus = FormzSubmissionStatus.initial,
    this.products = const [],
    this.productsStatus = FormzSubmissionStatus.initial,
    this.myTickets = const [],
    this.myTicketsStatus = FormzSubmissionStatus.initial,
    this.news = const [],
    this.newsStatus = FormzSubmissionStatus.initial,
    this.events = const [], // Initialize events
    this.eventsStatus = FormzSubmissionStatus.initial, // Initialize events status
    this.appUpdateStatus = AppUpdateStatus.none,
  });

  @override
  List<Object> get props => [
    matchCategories,
    matchCategoriesStatus,
    selectedMatchCategory,
    showSearch,
    game,
    gameStatus,
    products,
    productsStatus,
    myTickets,
    myTicketsStatus,
    news,
    newsStatus,
    events, // Add to props
    eventsStatus, // Add to props
    appUpdateStatus,
  ];

  HomeState copyWith({
    List<ItemEntity>? matchCategories,
    FormzSubmissionStatus? matchCategoriesStatus,
    List<ItemEntity>? selectedMatchCategory,
    bool? showSearch,
    GameEntity? game,
    FormzSubmissionStatus? gameStatus,
    List<ProductEntity>? products,
    FormzSubmissionStatus? productsStatus,
    List<TicketEntity>? myTickets,
    FormzSubmissionStatus? myTicketsStatus,
    List<NewsEntity>? news,
    FormzSubmissionStatus? newsStatus,
    List<NewsEntity>? events, // Add events parameter
    FormzSubmissionStatus? eventsStatus, // Add events status parameter
    AppUpdateStatus? appUpdateStatus,
  }) {
    return HomeState(
      matchCategories: matchCategories ?? this.matchCategories,
      matchCategoriesStatus: matchCategoriesStatus ?? this.matchCategoriesStatus,
      selectedMatchCategory: selectedMatchCategory ?? this.selectedMatchCategory,
      showSearch: showSearch ?? this.showSearch,
      game: game ?? this.game,
      gameStatus: gameStatus ?? this.gameStatus,
      products: products ?? this.products,
      productsStatus: productsStatus ?? this.productsStatus,
      myTickets: myTickets ?? this.myTickets,
      myTicketsStatus: myTicketsStatus ?? this.myTicketsStatus,
      news: news ?? this.news,
      newsStatus: newsStatus ?? this.newsStatus,
      events: events ?? this.events, // Add events assignment
      eventsStatus: eventsStatus ?? this.eventsStatus, // Add events status assignment
      appUpdateStatus: appUpdateStatus ?? this.appUpdateStatus,
    );
  }
}

final class HomeInitial extends HomeState {}