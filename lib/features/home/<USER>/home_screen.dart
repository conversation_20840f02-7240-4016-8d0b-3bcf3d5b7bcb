import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/services/notification_service.dart';
import 'package:echipta/core/utils/enums.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/features/home/<USER>/widgets/w_category_matches.dart';
import 'package:echipta/features/home/<USER>/widgets/w_home_appbar.dart';
import 'package:echipta/features/home/<USER>/widgets/w_products.dart';
import 'package:echipta/features/home/<USER>/widgets/w_technical_work_bottomsheet.dart';
import 'package:echipta/features/home/<USER>/widgets/w_update_bottomsheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final RefreshController _refreshController = RefreshController(
    initialRefresh: false,
  );
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    getAllData();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      context.read<HomeBloc>().add(GetAppVersionEvent());
      // Silently push FCM token to backend and ensure topic subscription on home visit
      NotificationService().syncFcmTokenWithServer();
      NotificationService().ensureLanguageTopicSubscription();
    });
  }

  Future<void> getAllData() async {
    context.read<HomeBloc>()
      ..add(GetMatchCategoriesEvent())
      ..add(GetGameEvent())
      ..add(GetProductsEvent())
      ..add(GetNewsEvent());
    await Future.delayed(const Duration(seconds: 1));
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<HomeBloc, HomeState>(
      listenWhen: (previous, current) {
        return previous.appUpdateStatus != current.appUpdateStatus;
      },
      listener: (BuildContext context, HomeState state) {
        switch (state.appUpdateStatus) {
          case AppUpdateStatus.notWorking:
            showTechnicalWorkDialog(context);
            break;
          case AppUpdateStatus.optional:
            showUpdateDialog(context, required: false);
            break;
          case AppUpdateStatus.required:
            showUpdateDialog(context, required: true);
            break;
          default:
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: AppColors.white,
          body: NotificationListener<ScrollNotification>(
            onNotification: (ScrollNotification notification) {
              // Detect when user is pulling down
              if (notification is ScrollUpdateNotification) {
                if (notification.metrics.pixels < -50 && !_isRefreshing) {
                  setState(() {
                    _isRefreshing = true;
                  });
                } else if (notification.metrics.pixels >= 0 && _isRefreshing) {
                  setState(() {
                    _isRefreshing = false;
                  });
                }
              }
              // Reset color when scroll ends
              if (notification is ScrollEndNotification && !_refreshController.isRefresh) {
                setState(() {
                  _isRefreshing = false;
                });
              }
              return false;
            },
            child: Stack(
              children: [
                Container(
                  height: 200,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.vertical(
                      bottom: Radius.circular(30),
                    ),
                    color: _isRefreshing ? AppColors.primary : AppColors.white,
                  ),
                ),
                SmartRefresher(
                  enablePullDown: true,
                  physics: const ClampingScrollPhysics(),
                  enablePullUp: false,
                  controller: _refreshController,
                  header: BezierHeader(
                    bezierColor: AppColors.primary2,
                    rectHeight: 60,
                    child: Center(
                      child: SvgPicture.asset(AppAssets.whiteLogo, height: 30),
                    ),
                  ),
                  onRefresh: () async {
                    setState(() {
                      _isRefreshing = true;
                    });
                    await getAllData();
                    if (mounted) {
                      setState(() {
                        _isRefreshing = false;
                      });
                    }
                    _refreshController.refreshCompleted();
                  },
                  child: CustomScrollView(
                    physics: ClampingScrollPhysics(),
                    slivers: [
                      const WHomeAppBar(),
                      const WGames(),
                      const WProducts(),
                      SliverToBoxAdapter(child: Gap(context.padding.bottom)),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}