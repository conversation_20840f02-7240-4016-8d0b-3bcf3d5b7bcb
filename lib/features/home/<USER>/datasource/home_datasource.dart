import 'package:dio/dio.dart';
import 'package:echipta/core/api/dio_settings.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/features/auth/data/models/item_model.dart';
import 'package:echipta/features/home/<USER>/models/app_version_model.dart';
import 'package:echipta/features/home/<USER>/models/game_model.dart';
import 'package:echipta/features/home/<USER>/models/news_model.dart';
import 'package:echipta/features/home/<USER>/models/product_model.dart';

abstract class HomeDatasource {
  Future<List<ItemModel>> getMatchCategories();
  Future<GameModel> getGame();
  Future<List<ProductModel>> getProducts();
  Future<Map<String, List<NewsModel>>> getNews();
  Future<AppVersionModel> getAppVersion();
}

class HomeDatasourceImpl implements HomeDatasource {
  final Dio _dio = serviceLocator<DioSettings>().dio;
  @override
  Future<List<ItemModel>> getMatchCategories() async {
    try {
      final response = await _dio.get("/games/get-match-categories");
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return (response.data["data"] as List).map((e) => ItemModel.fromJson(e as Map<String, dynamic>)).toList();
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<GameModel> getGame() async {
    try {
      final response = await _dio.get("/games/get-games");
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return GameModel.fromJson(response.data["data"]);
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<List<ProductModel>> getProducts() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get("/products/get-products", options: Options(headers: {"Authorization": token}));
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return (response.data["data"] as List).map((e) => ProductModel.fromJson(e as Map<String, dynamic>)).toList();
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<Map<String, List<NewsModel>>> getNews() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get(
          "/games/news",
          options: Options(headers: {"Authorization": "Bearer $token"})
      );

      if (response.statusCode != null && response.statusCode! >= 200 && response.statusCode! < 300) {
        final responseData = response.data;
        if (responseData != null && responseData is Map<String, dynamic>) {
          // Access the nested "data" object
          final data = responseData["data"];
          if (data != null && data is Map<String, dynamic>) {
            // Parse news list
            List<NewsModel> newsList = [];
            if (data["news"] != null && data["news"] is List) {
              newsList = (data["news"] as List)
                  .map((e) => NewsModel.fromJson(e as Map<String, dynamic>))
                  .toList();
            }

            // Parse events list
            List<NewsModel> eventsList = [];
            if (data["events"] != null && data["events"] is List) {
              eventsList = (data["events"] as List)
                  .map((e) => NewsModel.fromJson(e as Map<String, dynamic>))
                  .toList();
            }

            return {
              'news': newsList,
              'events': eventsList,
            };
          } else {
            throw CustomException(message: 'Invalid data format in response', code: '142');
          }
        } else {
          throw CustomException(message: 'Invalid response format', code: '142');
        }
      } else {
        final message = response.data != null
            ? (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]()]'), '')
            : 'Request failed';
        throw CustomException(message: message, code: '${response.statusCode ?? 0}');
      }
    } on DioException catch (error) {
      // Extract meaningful error message from DioException
      String errorMessage = 'Network error';
      String errorCode = '500';

      if (error.response != null) {
        errorCode = '${error.response!.statusCode ?? 500}';
        if (error.response!.data != null) {
          errorMessage = error.response!.data.toString();
        }
      } else if (error.message != null) {
        errorMessage = error.message!;
      }

      throw CustomException(message: errorMessage, code: errorCode);
    } on CustomException {
      rethrow;
    } catch (error) {
      throw CustomException(message: 'Unexpected error: $error', code: '141');
    }
  }

  @override
  Future<AppVersionModel> getAppVersion() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get("/settings/version", options: Options(headers: {"Authorization": token}));
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return AppVersionModel.fromJson(response.data["data"] as Map<String, dynamic>);
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }
}
