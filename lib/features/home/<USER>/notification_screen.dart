import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_empty.dart';
import 'package:echipta/features/common/widgets/w_error.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/generated/assets.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class NotificationScreen extends StatelessWidget {
  const NotificationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            LocaleKeys.notification.tr(),
            style: context.textTheme.displaySmall,
          ),
          bottom: TabBar(
            labelStyle: context.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            tabs: [
              Tab(text: "Yangiliklar"), // News
              Tab(text: "Bildirishnomalar"), // Notifications
            ],
            indicatorColor: AppColors.primary,
            labelColor: AppColors.primary,
            unselectedLabelColor: Colors.grey,
            indicatorWeight: 3,
            indicatorSize: TabBarIndicatorSize.tab,
            indicatorPadding: EdgeInsets.symmetric(horizontal: 20),
            dividerHeight: 0,
          ),
        ),
        body: TabBarView(
          children: [
            // News Tab (Full notifications)
            _NewsTab(),
            // Events Tab (Compact notifications)
            _EventsTab(),
          ],
        ),
      ),
    );
  }
}

class _NewsTab extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        if (state.newsStatus.isInProgress) {
          return Center(child: CircularProgressIndicator.adaptive());
        } else if (state.newsStatus.isFailure) {
          return WError();
        } else if (state.newsStatus.isSuccess && state.news.isEmpty) {
          return WEmptyScreen();
        }

        return ListView.separated(
          padding: EdgeInsets.all(20),
          separatorBuilder: (context, index) => Gap(10),
          itemCount: state.news.length,
          itemBuilder: (context, index) {
            final item = state.news[index];
            return InkWell(
              onTap: () {
                context.push(AppRouter.notificationDetail, extra: item.id);
              },
              child: Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(21),
                  color: AppColors.white,
                  boxShadow: [
                    BoxShadow(
                      offset: Offset(0, 4),
                      blurRadius: 4,
                      spreadRadius: 0,
                      color: AppColors.black.withOpacity(0.25),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Hero(
                      tag: 'news_image_${item.id}',
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(21),
                        child: CachedNetworkImage(
                          imageUrl: item.image.replaceAll(
                            'https://dev.echipta.uz/uploads/https://',
                            'https://',
                          ),
                          height: 168,
                          width: double.maxFinite,
                          fit: BoxFit.cover,
                          placeholder:
                              (context, url) => Container(
                                height: 168,
                                width: double.maxFinite,
                                color: AppColors.primary.withOpacity(0.1),
                                child: Center(
                                  child: CircularProgressIndicator(
                                    color: AppColors.primary,
                                  ),
                                ),
                              ),
                          errorWidget:
                              (context, url, error) => Container(
                                height: 168,
                                width: double.maxFinite,
                                color: AppColors.primary,
                                child: Center(
                                  child: SvgPicture.asset(
                                    AppAssets.whiteLogo,
                                    color: AppColors.white.withOpacity(0.5),
                                    height: 50,
                                  ),
                                ),
                              ),
                        ),
                      ),
                    ),
                    Gap(10),
                    Hero(
                      tag: 'news_title_${item.id}',
                      child: Material(
                        color: Colors.transparent,
                        child: Text(
                          item.title,
                          style: context.textTheme.bodyLarge,
                        ),
                      ),
                    ),
                    Hero(
                      tag: 'news_content_${item.id}',
                      child: Material(
                        color: Colors.transparent,
                        child: Text(
                          item.text,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    Gap(10),
                    Align(
                      alignment: Alignment.bottomRight,
                      child: Text(
                        DateFormat(
                          "dd MMMM yyyy",
                          context.locale.languageCode,
                        ).format(
                          DateTime.tryParse(item.datetime) ?? DateTime.now(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class _EventsTab extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        if (state.eventsStatus.isInProgress) {
          return Center(child: CircularProgressIndicator.adaptive());
        } else if (state.eventsStatus.isFailure) {
          return WError();
        } else if (state.events.isEmpty) {
          return WEmptyScreen();
        }

        return ListView.separated(
          padding: EdgeInsets.all(20),
          separatorBuilder: (context, index) => Gap(10),
          itemCount: state.events.length,
          itemBuilder: (context, index) {
            final item = state.events[index];
            return InkWell(
              onTap: () {
                context.push(AppRouter.notificationDetail, extra: item.id);
              },
              child: Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(21),
                  color: AppColors.white,
                  boxShadow: [
                    BoxShadow(
                      offset: Offset(0, 4),
                      blurRadius: 4,
                      spreadRadius: 0,
                      color: AppColors.black.withOpacity(0.25),
                    ),
                  ],
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Event icon or small image
                    item.image.isNotEmpty
                        ? ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: CachedNetworkImage(
                            imageUrl: item.image.replaceAll(
                              'https://dev.echipta.uz/uploads/https://',
                              'https://',
                            ),
                            fit: BoxFit.cover,
                            errorWidget:
                                (context, url, error) => SvgPicture.asset(
                                  Assets.iconsBlueLogo,
                                  height: 30,
                                ),
                          ),
                        )
                        : SvgPicture.asset(Assets.iconsBlueLogo, height: 30),
                    Gap(12),
                    // Content
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.title,
                            style: context.textTheme.bodyLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Gap(4),
                          if (item.text.isNotEmpty)
                            Text(
                              item.text,
                              style: context.textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          Gap(8),
                          Align(
                            alignment: Alignment.bottomRight,
                            child: Text(
                              DateFormat(
                                "dd MMM yyyy",
                                context.locale.languageCode,
                              ).format(
                                DateTime.tryParse(item.datetime) ??
                                    DateTime.now(),
                              ),
                              style: context.textTheme.bodySmall?.copyWith(
                                color: Colors.grey[500],
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
