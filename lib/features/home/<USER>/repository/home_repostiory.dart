import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/auth/domain/entities/item_entity.dart';
import 'package:echipta/features/home/<USER>/entities/app_version_entity.dart';
import 'package:echipta/features/home/<USER>/entities/game_entity.dart';
import 'package:echipta/features/home/<USER>/entities/news_entity.dart';
import 'package:echipta/features/home/<USER>/entities/product_entity.dart';

abstract class HomeRepostiory {
  Future<Either<Failure, List<ItemEntity>>> getMatchCategories();
  Future<Either<Failure, GameEntity>> getGame();
  Future<Either<Failure, List<ProductEntity>>> getProducts();
  Future<Either<Failure, Map<String, List<NewsEntity>>>> getNews();
  Future<Either<Failure, AppVersionEntity>> getAppVersion();
}
