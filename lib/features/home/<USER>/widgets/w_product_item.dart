import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/home/<USER>/entities/product_entity.dart';
import 'package:echipta/features/home/<USER>/widgets/w_product_detail.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:easy_localization/easy_localization.dart';

class WProcutItem extends StatelessWidget {
  const WProcutItem({super.key, required this.item});

  final ProductEntity item;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, 0),
            blurRadius: 84,
            spreadRadius: 0,
            color: AppColors.primary.withOpacity(0.1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [

          //Cached image with loading
          CachedNetworkImage(
            imageUrl: item.image,
            width: double.maxFinite,
            height: 160,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              width: double.maxFinite,
              height: 160,
              color: AppColors.primary.withOpacity(0.1),
              child: const Center(
                child: CircularProgressIndicator.adaptive(),
              ),
            )),

          const Gap(10),
          Padding(
            padding: const EdgeInsets.only(left: 4, right: 4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ///Make text 1 line and rest ellipsis
                Text(
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  context.locale.languageCode == "uz"
                      ? item.title_uz
                      : item.title_ru,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                Text(
                  LocaleKeys.price.tr(
                    namedArgs: {
                      "price": item.price.toDouble().formatAsSpaceSeparated(),
                    },
                  ),
                  style: Theme.of(
                    context,
                  ).textTheme.displaySmall!.copyWith(color: AppColors.primary),
                ),
                const Gap(5),
                GestureDetector(
                  onTap: () {
                    showProductDetail(context, item);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    width: double.maxFinite,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 10,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(14),
                      color: AppColors.primary,
                    ),
                    child: Text(
                      LocaleKeys.orderProduct.tr(),
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall!.copyWith(color: AppColors.white),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
