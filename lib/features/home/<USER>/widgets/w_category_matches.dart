import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/features/home/<USER>/widgets/w_game_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';

class WGames extends StatelessWidget {
  const WGames({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        if (state.gameStatus.isInProgress) {
          return const SliverFillRemaining(
            child: Center(child: CircularProgressIndicator.adaptive()),
          );
        } else if (state.gameStatus.isSuccess) {
          return SliverList(
            delegate: SliverChildListDelegate([
              Container(
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 20,
                ),
                child: ListView.separated(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: state.game.categoryMatches.length,
         ///If item.matches.isEmpty dont add separator gap, else add
                    separatorBuilder: (context, index) =>
                    state.game.categoryMatches[index].matches.isEmpty
                        ? const Gap(0)
                        : const Gap(10),
                  itemBuilder: (context, index) {
                    final item = state.game.categoryMatches[index];
                    return WGameItem(item: item);
                  },
                ),
              ),
            ]),
          );
        } else {
          return const SliverToBoxAdapter(child: SizedBox());
        }
      },
    );
  }
}
