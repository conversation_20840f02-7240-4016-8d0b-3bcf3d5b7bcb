import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/core/utils/size_config.dart';
import 'package:echipta/features/auth/domain/entities/item_entity.dart';
import 'package:echipta/features/order/presentation/widgets/w_match_item.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class WGameItem extends StatelessWidget {
  const WGameItem({super.key, required this.item});

  final ItemEntity item;

  @override
  Widget build(BuildContext context) {
    return item.matches.isEmpty
        ?  SizedBox()
        : Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                CachedNetworkImage(
                  imageUrl: item.image,
                  width: wi(30),
                  height: he(30),
                  errorWidget: (context, url, error) => const Icon(Icons.image),
                ),
                const Gap(6),
                Center(
                  child: Text(
                    item.title.isEmpty ? "…" : item.title,
                    style: context.textTheme.displaySmall,
                  ),
                ),
              ],
            ),
            const Gap(12),
            ListView.separated(
              shrinkWrap: true,
              separatorBuilder: (context, index) => const Gap(10),
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: item.matches.length,
              itemBuilder: (context, index) {
                final match = item.matches[index];
                return WMatchItem(item: match);
              },
            ),
          ],
        );
  }
}
