import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/services/notification_service.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:go_router/go_router.dart';
class SelectLanguageScreen extends StatefulWidget {
  const SelectLanguageScreen({super.key});

  @override
  State<SelectLanguageScreen> createState() => _SelectLanguageScreenState();
}

class _SelectLanguageScreenState extends State<SelectLanguageScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding:
              EdgeInsets.fromLTRB(20.0, 20, 20, context.padding.bottom + 20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(),
              SvgPicture.asset(AppAssets.blueLogoFull),
              const Gap(24),
              GestureDetector(
                onTap: () async {
                  context.setLocale(const Locale("uz"));
                  await StorageRepository.putString(StoreKeys.language, "uz");
                  // Update FCM topic subscription to match new language
                  NotificationService().ensureLanguageTopicSubscription();
                },
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                      border: context.locale.languageCode == "uz"
                          ? Border.all(color: AppColors.primary)
                          : null,
                      color: AppColors.mediumGrey,
                      borderRadius: BorderRadius.circular(200)),
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(AppAssets.uz),
                      const Gap(10),
                      Text(
                        "O‘zbek tili",
                        style: context.textTheme.labelLarge,
                      )
                    ],
                  ),
                ),
              ),
              const Gap(16),
              GestureDetector(
                onTap: () async {
                  context.setLocale(const Locale("ru"));
                  await StorageRepository.putString(StoreKeys.language, "ru");
                  // Update FCM topic subscription to match new language
                  NotificationService().ensureLanguageTopicSubscription();
                },
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                      border: context.locale.languageCode == "ru"
                          ? Border.all(color: AppColors.primary)
                          : null,
                      color: AppColors.mediumGrey,
                      borderRadius: BorderRadius.circular(200)),
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(AppAssets.ru),
                      const Gap(10),
                      Text(
                        "Русский язык",
                        style: context.textTheme.labelLarge,
                      )
                    ],
                  ),
                ),
              ),
              const Spacer(),
              WButton(onTap: () {
                context.go(AppRouter.auth);
              }, txt:  LocaleKeys.confirm.tr())
            ],
          ),
        ),
      ),
    );
  }
}
