import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/home/<USER>/entities/match_stadium_entity.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart' as launcher;

class WStadium extends StatefulWidget {
  const WStadium({super.key, required this.stadium});

  final MatchStadiumEntity stadium;

  @override
  State<WStadium> createState() => _WStadiumState();
}

class _WStadiumState extends State<WStadium> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.65,
      padding: EdgeInsets.fromLTRB(20, 20, 20, 20 + context.padding.bottom),
      width: double.maxFinite,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.darkGrey.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.stadium,
                  color: AppColors.primary,
                  size: 20,
                ),
              ),
              const Gap(12),
              Expanded(
                child: Text(
                  "${widget.stadium.name} stadioni",
                  style: context.textTheme.displaySmall!.copyWith(
                    color: AppColors.darkGrey,
                  ),
                ),
              ),
            ],
          ),
          const Gap(16),
          _buildMapImage(),
          const Gap(16),
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: AppColors.darkGrey,
                size: 16,
              ),
              const Gap(4),
              Expanded(
                child: Text(
                  widget.stadium.address,
                  style: context.textTheme.bodyMedium!.copyWith(
                    color: AppColors.darkGrey,
                  ),
                ),
              ),
            ],
          ),
          const Gap(12),
          Row(
            children: [
              Icon(
                Icons.people,
                color: AppColors.darkGrey,
                size: 16,
              ),
              const Gap(4),
              Text(
                "Stadion sig'imi: ${widget.stadium.count_sectors} dona",
                style: context.textTheme.bodyMedium!.copyWith(
                  color: AppColors.darkGrey,
                ),
              ),
            ],
          ),
          const Gap(20),
          MaterialButton(
            height: 56,
            elevation: 0,
            color: AppColors.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(200),
            ),
            onPressed: () async {
              await _openDirections(context);
            },
            child: Center(
              child: Text(
                "Yo'nalish olish",
                style: context.textTheme.labelLarge!.copyWith(
                  color: AppColors.white,
                ),
              ),
            ),
          ),
          const Gap(16),
          if (widget.stadium.images.isNotEmpty) ...[
            Text(
              "Stadion rasmlari",
              style: context.textTheme.titleMedium!.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Gap(12),
            Expanded(
              child: _buildStadiumImages(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMapImage() {
    return GestureDetector(
      onTap: () => _openLocation(context),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Container(
          height: 150,
          width: double.maxFinite,
          decoration: BoxDecoration(
            color: AppColors.fillColor,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Stack(
            alignment: Alignment.center,
            fit: StackFit.expand,
            children: [
              CachedNetworkImage(
                fit: BoxFit.cover,
                imageUrl:
                "https://static-maps.yandex.ru/1.x/?ll=${widget.stadium.longitude},${widget.stadium.latitude}&z=16&size=600,450&l=map&pt=${widget.stadium.longitude},${widget.stadium.latitude},pm2rdl",
                placeholder: (context, url) => _buildMapPlaceholder(),
                errorWidget: (context, url, error) => _buildMapError(),
                httpHeaders: const {
                  'User-Agent': 'Mozilla/5.0 (compatible; Flutter app)',
                },
              ),
              // Overlay to indicate it's clickable
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.open_in_new,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMapPlaceholder() {
    return Shimmer.fromColors(
      baseColor: AppColors.fillColor,
      highlightColor: AppColors.white,
      child: Container(
        height: 150,
        width: double.maxFinite,
        decoration: BoxDecoration(
          color: AppColors.fillColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Stack(
          children: [
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.map,
                      size: 32,
                      color: AppColors.primary.withOpacity(0.3),
                    ),
                  ),
                  const Gap(8),
                  Container(
                    width: 100,
                    height: 10,
                    decoration: BoxDecoration(
                      color: AppColors.mediumGrey.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(5),
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppColors.white.withOpacity(0.9),
                  shape: BoxShape.circle,
                ),
                child: SizedBox(
                  width: 12,
                  height: 12,
                  child: CircularProgressIndicator(
                    strokeWidth: 1.5,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppColors.primary.withOpacity(0.7),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMapError() {
    return Container(
      height: 150,
      width: double.maxFinite,
      decoration: BoxDecoration(
        color: AppColors.fillColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.red.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.red.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.map_outlined,
              size: 32,
              color: AppColors.red.withOpacity(0.7),
            ),
          ),
          const Gap(8),
          Text(
            "Xarita yuklanmadi",
            style: TextStyle(
              color: AppColors.darkGrey,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Gap(4),
          Text(
            "Internetni tekshiring",
            style: TextStyle(
              color: AppColors.lightGrey,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStadiumImages() {
    return ListView.separated(
      separatorBuilder: (context, index) => const Gap(12),
      itemCount: widget.stadium.images.length,
      scrollDirection: Axis.horizontal,
      itemBuilder: (context, index) {
        final item = widget.stadium.images[index];
        return ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Container(
            width: 150,
            height: 150,
            decoration: BoxDecoration(
              color: AppColors.fillColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: CachedNetworkImage(
              fit: BoxFit.cover,
              imageUrl: item.image,
              placeholder: (context, url) => _buildImagePlaceholder(),
              errorWidget: (context, url, error) => _buildImageError(),
              httpHeaders: const {
                'User-Agent': 'Mozilla/5.0 (compatible; Flutter app)',
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildImagePlaceholder() {
    return Shimmer.fromColors(
      baseColor: AppColors.fillColor,
      highlightColor: AppColors.white,
      child: Container(
        width: 150,
        height: 150,
        decoration: BoxDecoration(
          color: AppColors.fillColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Stack(
          children: [
            Center(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.image,
                  size: 24,
                  color: AppColors.primary.withOpacity(0.3),
                ),
              ),
            ),
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.all(3),
                decoration: BoxDecoration(
                  color: AppColors.white.withOpacity(0.9),
                  shape: BoxShape.circle,
                ),
                child: SizedBox(
                  width: 10,
                  height: 10,
                  child: CircularProgressIndicator(
                    strokeWidth: 1,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppColors.primary.withOpacity(0.7),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageError() {
    return Container(
      width: 150,
      height: 150,
      decoration: BoxDecoration(
        color: AppColors.fillColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.red.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.red.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.broken_image_outlined,
              size: 20,
              color: AppColors.red.withOpacity(0.7),
            ),
          ),
          const Gap(4),
          Text(
            "Yuklanmadi",
            style: TextStyle(
              color: AppColors.darkGrey,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _openLocation(BuildContext context) async {
    final latitude = widget.stadium.latitude;
    final longitude = widget.stadium.longitude;

    try {
      bool launched = false;

      if (Platform.isIOS) {
        // iOS: Open location in Apple Maps (no directions, just show location)
        final appleUrl = Uri.parse('http://maps.apple.com/?ll=$latitude,$longitude&q=$latitude,$longitude');

        try {
          launched = await launcher.launchUrl(
            appleUrl,
            mode: launcher.LaunchMode.externalApplication,
          );
        } catch (e) {
          print('Apple Maps failed: $e');
        }

        // Fallback to Google Maps for iOS
        if (!launched) {
          final googleUrl = Uri.parse('https://www.google.com/maps/search/?api=1&query=$latitude,$longitude');
          try {
            launched = await launcher.launchUrl(
              googleUrl,
              mode: launcher.LaunchMode.externalApplication,
            );
          } catch (e) {
            print('Google Maps fallback failed: $e');
          }
        }
      } else if (Platform.isAndroid) {
        // Android: Open location in Google Maps (no navigation, just show location)
        final googleMapsUrl = Uri.parse('geo:$latitude,$longitude?q=$latitude,$longitude');

        try {
          launched = await launcher.launchUrl(
            googleMapsUrl,
            mode: launcher.LaunchMode.externalApplication,
          );
        } catch (e) {
          print('Google Maps app failed: $e');

          // Fallback to web Google Maps
          final webUrl = Uri.parse('https://www.google.com/maps/search/?api=1&query=$latitude,$longitude');
          try {
            launched = await launcher.launchUrl(
              webUrl,
              mode: launcher.LaunchMode.externalApplication,
            );
          } catch (e) {
            print('Web Google Maps fallback failed: $e');
          }
        }
      } else {
        // Other platforms: Use web Google Maps
        final webUrl = Uri.parse('https://www.google.com/maps/search/?api=1&query=$latitude,$longitude');
        launched = await launcher.launchUrl(
          webUrl,
          mode: launcher.LaunchMode.externalApplication,
        );
      }

      if (!launched) {
        _showErrorSnackbar(context, "Xaritani ochishda xatolik yuz berdi");
      }
    } catch (e) {
      print('Location error: $e');
      _showErrorSnackbar(context, "Xaritani ochishda xatolik yuz berdi");
    }
  }

  Future<void> _openDirections(BuildContext context) async {
    final latitude = widget.stadium.latitude;
    final longitude = widget.stadium.longitude;

    try {
      bool launched = false;

      if (Platform.isIOS) {
        // iOS: Try Apple Maps first, then Google Maps as fallback
        final appleUrl = Uri.parse('http://maps.apple.com/?daddr=$latitude,$longitude');

        try {
          launched = await launcher.launchUrl(
            appleUrl,
            mode: launcher.LaunchMode.externalApplication,
          );
        } catch (e) {
          print('Apple Maps failed: $e');
        }

        // Fallback to Google Maps for iOS
        if (!launched) {
          final googleUrl = Uri.parse('https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude');
          try {
            launched = await launcher.launchUrl(
              googleUrl,
              mode: launcher.LaunchMode.externalApplication,
            );
          } catch (e) {
            print('Google Maps fallback failed: $e');
          }
        }
      } else if (Platform.isAndroid) {
        // Android: Try Google Maps intent first, then web fallback
        final googleMapsUrl = Uri.parse('google.navigation:q=$latitude,$longitude');

        try {
          launched = await launcher.launchUrl(
            googleMapsUrl,
            mode: launcher.LaunchMode.externalApplication,
          );
        } catch (e) {
          print('Google Maps app failed: $e');

          // Fallback to web Google Maps
          final webUrl = Uri.parse('https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude');
          try {
            launched = await launcher.launchUrl(
              webUrl,
              mode: launcher.LaunchMode.externalApplication,
            );
          } catch (e) {
            print('Web Google Maps fallback failed: $e');
          }
        }
      } else {
        // Other platforms: Use web Google Maps
        final webUrl = Uri.parse('https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude');
        launched = await launcher.launchUrl(
          webUrl,
          mode: launcher.LaunchMode.externalApplication,
        );
      }

      if (!launched) {
        _showErrorSnackbar(context, "Xaritani ochishda xatolik yuz berdi");
      }
    } catch (e) {
      print('Directions error: $e');
      _showErrorSnackbar(context, "Xaritani ochishda xatolik yuz berdi");
    }
  }

  void _showErrorSnackbar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }
}
