import 'dart:async';

import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/features/order/presentation/widgets/w_error_modal_popup.dart';
import 'package:echipta/features/order/presentation/widgets/w_order_id_payment_type.dart';
import 'package:echipta/features/order/presentation/widgets/w_order_idcard_info.dart';
import 'package:echipta/features/order/presentation/widgets/w_success_modal_popup.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';

class OrderIdcardScreen extends StatefulWidget {
  const OrderIdcardScreen({super.key});

  @override
  State<OrderIdcardScreen> createState() => _OrderIdcardScreenState();
}

class _OrderIdcardScreenState extends State<OrderIdcardScreen> {
  late Timer _timer;
  int _start = 600; // Starting value of the countdown in seconds

  @override
  void initState() {
    super.initState();
    startTimer();
  }

  void startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_start > 0) {
        setState(() {
          _start--;
        });
      } else {
        _timer.cancel(); // Stop the timer when countdown reaches 0
      }
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        surfaceTintColor: AppColors.primary,
        foregroundColor: AppColors.white,
        title: Text(
          "To'lov qilish",
          style: context.textTheme.displayMedium!.copyWith(
            color: AppColors.white,
          ),
        ),
      ),
      body: BlocBuilder<OrderBloc, OrderState>(
        builder: (context, state) {
          return ListView(
            padding: EdgeInsets.all(20),
            children: [
              WOrderIdCardInfo(start: _start),
              Gap(20),
              WOrderIdPaymentType(),
              Gap(20),
              BlocBuilder<ProfileBloc, ProfileState>(
                builder: (context, profileState) {
                  return BlocBuilder<HomeBloc, HomeState>(
                    builder: (context, homeState) {
                      return WButton(
                        onTap: () {
                          final ticket = state.ticket;
                          context.read<OrderBloc>().add(
                            OrderIdEvent(
                              onSuccess: (p0) async {
                                // Handle null response
                                if (p0 == null) {
                                  showModalBottomSheet(
                                    context: context,
                                    showDragHandle: true,
                                    builder: (context) {
                                      return const WErrorModalPopup(
                                        title: "Xatolik yuz berdi!",
                                        message: "Ma'lumot olinmadi. Iltimos qaytadan urinib ko'ring.",
                                      );
                                    },
                                  );
                                  return;
                                }

                                // Handle Map response (new structure)
                                if (p0 is Map<String, dynamic>) {
                                  if (p0["error"] == true) {
                                    // Show error message
                                    Fluttertoast.showToast(
                                      msg: p0["message"] ?? "Error occurred",
                                      backgroundColor: AppColors.red,
                                    );
                                    return;
                                  } else {
                                    // Success case with payment URL
                                    if (state.paymentType == OrderPaymentType.alif) {
                                      String paymentUrl = p0["payment_url"] ?? "";
                                      if (paymentUrl.isNotEmpty) {
                                        Uri _url = Uri.parse(paymentUrl);
                                        bool launched = await launchUrl(
                                          _url,
                                          mode: LaunchMode.inAppWebView,
                                        );
                                        if (!launched) {
                                          throw Exception('Could not launch $_url');
                                        } else {
                                          // Navigate to status screen after launching payment
                                          final orderId = p0["order_id"];
                                          Future.delayed(Duration(seconds: 2), () {
                                            if (mounted) {
                                              // Ensure orderId is an int, default to 0 if null or invalid
                                              int safeOrderId = 0;
                                              if (orderId != null) {
                                                if (orderId is int) {
                                                  safeOrderId = orderId;
                                                } else if (orderId is String) {
                                                  safeOrderId = int.tryParse(orderId) ?? 0;
                                                }
                                              }
                                              context.push(AppRouter.orderStatus, extra: safeOrderId);
                                            }
                                          });
                                        }
                                      }
                                    } else {
                                      showModalBottomSheet(
                                        context: context,
                                        showDragHandle: true,
                                        builder: (context) {
                                          return WSuccessModalPopup(
                                            title: "Arizangiz qabul qilindi",
                                            txt:
                                                "Mavsumiy kartangiz ko`rib chiqish jarayonid.... Tez orada xabar olasiz!",
                                          );
                                        },
                                      );
                                    }
                                  }
                                }
                                // Handle legacy string response (for backward compatibility)
                                else if (p0 is String) {
                                  if (p0 == "Balance not enough") {
                                    Fluttertoast.showToast(
                                      msg: p0,
                                      backgroundColor: AppColors.red,
                                    );
                                  } else {
                                    // Assume it's a payment URL
                                    if (state.paymentType == OrderPaymentType.alif) {
                                      Uri _url = Uri.parse(p0);
                                      bool launched = await launchUrl(
                                        _url,
                                        mode: LaunchMode.inAppWebView,
                                      );
                                      if (!launched) {
                                        throw Exception('Could not launch $_url');
                                      } else {
                                        // Navigate to status screen after launching payment
                                        // For string response, we need to extract order ID differently
                                        // This is a fallback case, so we'll use a placeholder
                                        Future.delayed(Duration(seconds: 2), () {
                                          if (mounted) {
                                            // Since this is a string response, we don't have order_id
                                            // We'll need to handle this case differently
                                            context.push(AppRouter.orderStatus, extra: 0);
                                          }
                                        });
                                      }
                                    } else {
                                      showModalBottomSheet(
                                        context: context,
                                        showDragHandle: true,
                                        builder: (context) {
                                          return WSuccessModalPopup(
                                            title: "Arizangiz qabul qilindi",
                                            txt:
                                                "Mavsumiy kartangiz ko`rib chiqish jarayonid.... Tez orada xabar olasiz!",
                                          );
                                        },
                                      );
                                    }
                                  }
                                }
                              },
                              onError: (errorMessage) {
                                showModalBottomSheet(
                                  context: context,
                                  showDragHandle: true,
                                  builder: (context) {
                                    return WErrorModalPopup(
                                      title: "Xatolik yuz berdi!",
                                      message: errorMessage,
                                    );
                                  },
                                );
                              },
                              match_id: homeState.game.currentMatch?.id ?? 0,
                              sector: ticket.sector,
                              row: ticket.row,
                              seat: ticket.seat,
                              paymentType: state.paymentType.name,
                              type: profileState.idCardType.name,
                              city: 1,
                            ),
                          );
                        },
                        txt: "To'lovni amalga oshirish",
                      );
                    },
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }
}
