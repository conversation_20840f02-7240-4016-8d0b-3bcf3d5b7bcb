name: echipta
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+24

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  bloc: ^9.0.0
  flutter_bloc: ^9.1.0
  intl: ^0.20.2
  easy_localization: ^3.0.8
  equatable: ^2.0.7
  get_it: ^8.0.3
  dio: ^5.8.0+1
  go_router: ^14.8.1
  flutter_svg: ^2.0.17
  gap: ^3.0.1
  mask_text_input_formatter: ^2.9.0
  pinput: ^5.0.1
  formz: ^0.8.0
  json_annotation: ^4.9.0
  json_serializable: ^6.9.4
  dropdown_button2: ^2.3.9
  vibration: ^3.1.3
  modal_bottom_sheet: ^3.0.0
  cached_network_image: ^3.4.1
  webview_flutter: ^4.10.0
  data_table_2: ^2.5.15
  image: ^4.1.7
  grouped_list: ^6.0.0
  flutter_widget_from_html: ^0.16.0
  url_launcher: ^6.3.1
  fluttertoast: ^8.2.12
  image_picker: ^1.1.2
  panorama_viewer: ^2.0.4
  google_fonts: ^6.2.1
  no_screenshot: ^0.3.1
  keyboard_dismisser: ^3.0.0
  top_snackbar_flutter: ^3.2.0
  flutter_alice: ^2.0.1
  overlay_support: ^2.1.0
  percent_indicator: ^4.2.5
  package_info_plus: ^8.3.0
  lottie: ^3.3.1
  ticketcher: ^0.3.0
  pull_to_refresh: ^2.0.0
  # Push Notifications
  firebase_core: ^3.6.0
  firebase_messaging: ^15.1.3
  flutter_local_notifications: ^18.0.1
  device_info_plus: ^10.1.2
  shared_preferences: ^2.3.2
  flutter_timezone: ^3.0.1
  timezone: ^0.9.4
  internet_connection_checker: ^1.0.0+1
  permission_handler: ^11.3.1
  http: ^1.1.0
  flip_card: ^0.7.0
  shimmer: ^3.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.15
  rename: ^3.0.2

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/fonts/
    - assets/icons/
    - assets/animations/
    - assets/locales/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    # Poppins Font Family
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/Poppins-Light.ttf
          weight: 300
        - asset: assets/fonts/Poppins-Regular.ttf
          weight: 400
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/Poppins-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Poppins-Black.ttf
          weight: 900

    # Segoe UI Font Family - Microsoft's modern UI font
    # Excellent for readability and digital interfaces
    - family: SegoeUI
      fonts:
        # Light weights
        - asset: assets/fonts/Segoe_Light_300.ttf
          weight: 300
        - asset: assets/fonts/Segoe_Light_Italic_300.ttf
          weight: 300
          style: italic

        # Regular weights
        - asset: assets/fonts/Segoe_400.ttf
          weight: 400
        - asset: assets/fonts/Segoe_Italique_400.ttf
          weight: 400
          style: italic

        # Medium/Semibold weights
        - asset: assets/fonts/Segoe_Semibold_600.ttf
          weight: 600
        - asset: assets/fonts/Segoe_Semibold_Italic_600.ttf
          weight: 600
          style: italic

        # Bold weights
        - asset: assets/fonts/Segoe_Gras_700.ttf
          weight: 700
        - asset: assets/fonts/Segoe_Gras_Italique_700.ttf
          weight: 700
          style: italic

        # Black weights
        - asset: assets/fonts/Segoe_Black_900.ttf
          weight: 900
        - asset: assets/fonts/Segoe_Black_Italic_900.ttf
          weight: 900
          style: italic
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
